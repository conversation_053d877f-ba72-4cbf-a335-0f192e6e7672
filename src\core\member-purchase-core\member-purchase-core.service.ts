import { Injectable } from '@nestjs/common';
import { MemberPurchase, MEMBERSHIP_STATUS, Prisma } from '@prisma/client';
import { PrismaBaseRepository } from 'src/shared/libs/prisma-base.repository';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { MemberPurchaseCorePaginateDto } from './dto/member-purchase-core.dto';
import { memberPurchaseMessage } from 'src/shared/keys/helper.key';

@Injectable()
export class MemberPurchaseCoreService extends PrismaBaseRepository<
  MemberPurchase,
  MemberPurchaseCorePaginateDto,
  Prisma.MemberPurchaseCreateArgs,
  Prisma.MemberPurchaseUpdateArgs,
  Prisma.MemberPurchaseUpdateManyArgs,
  Prisma.MemberPurchaseFindUniqueArgs,
  Prisma.MemberPurchaseFindFirstArgs,
  Prisma.MemberPurchaseFindManyArgs,
  Prisma.MemberPurchaseDeleteArgs,
  Prisma.MemberPurchaseDeleteManyArgs,
  Prisma.MemberPurchaseCountArgs
> {
  constructor(public prisma: PrismaService) {
    super(prisma.prisma.memberPurchase, {
      NOT_FOUND: memberPurchaseMessage.PURCHASE_NOT_FOUND,
      DELETED: memberPurchaseMessage.PURCHASE_IS_DELETED,
    });
  }

  async findActivePurchaseByUserAndCommunity(
    userId: string,
    communityId: string,
  ) {
    return this.findFirst({
      where: {
        memberId: userId,
        communityId: communityId,
        membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
      },
    });
  }

  async findPurchasesByUser(userId: string) {
    return this.findMany({
      where: {
        memberId: userId,
      },
      include: {
        community: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                profileImage: true,
              },
            },
            price: true,
          },
        },
      },
    });
  }
}
