import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Body,
  Query,
  Param,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { CommunityService } from './community.service';
import { UserLoginJwtGuard } from 'src/module/auth/guards/firebase-auth.guard';
import { GetUserSession } from 'src/shared/decorators/user-session.decorator';
import { UserSessionType } from 'src/shared/types/user-session.type';
import {
  CreateCommunityDto,
  UpdateCommunityDto,
  UpdateCommunityStatusDto,
  JoinCommunityDto,
  CreateAppleCommunitySubscriptionDto,
  SubmitAppleCommunityForReviewDto,
} from './dto/community.dto';
import { BaseQueryCoreDto } from 'src/core/base-query-core/dto/base-query-core.dto';

@ApiTags('Community (Google Things Done / Apple Things Under Development)')
@ApiBearerAuth()
@UseGuards(UserLoginJwtGuard)
@Controller('community')
export class CommunityController {
  constructor(private readonly communityService: CommunityService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new community (Owner only)' })
  async createCommunity(
    @Body() dto: CreateCommunityDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.createCommunity(dto, sessionData);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update community details (Owner only)' })
  async updateCommunity(
    @Param('id') communityId: string,
    @Body() dto: UpdateCommunityDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.updateCommunity(communityId, dto, sessionData);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete community (Owner only)' })
  async deleteCommunity(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.deleteCommunity(communityId, sessionData);
  }

  // @Patch(':id/status')
  // @ApiOperation({ summary: 'Update community status (Owner only)' })
  // async updateCommunityStatus(
  //   @Param('id') communityId: string,
  //   @Body() dto: UpdateCommunityStatusDto,
  //   @GetUserSession() sessionData: UserSessionType,
  // ) {
  //   return this.communityService.updateCommunityStatus(communityId, dto, sessionData);
  // }

  @Get('owner')
  @ApiOperation({ summary: 'Get communities created by owner' })
  async getOwnerCommunities(
    @Query() baseQueryCoreDto: BaseQueryCoreDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.getOwnerCommunities(
      baseQueryCoreDto,
      sessionData,
    );
  }

  @Post('join')
  @ApiOperation({ summary: 'Join a community (Member)' })
  async joinCommunity(
    @Body() dto: JoinCommunityDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.joinCommunity(dto, sessionData);
  }

  @Get('purchases')
  @ApiOperation({ summary: 'Get member purchases/joined communities' })
  async getMemberPurchases(
    @Query() baseQueryCoreDto: BaseQueryCoreDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.getMemberPurchases(
      baseQueryCoreDto,
      sessionData,
    );
  }

  @Get(':id/members')
  @ApiOperation({ summary: 'Get community members (Owner only)' })
  async getCommunityMembers(
    @Param('id') communityId: string,
    @Query() baseQueryCoreDto: BaseQueryCoreDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.getCommunityMembers(
      communityId,
      baseQueryCoreDto,
      sessionData,
    );
  }

  @Get('available')
  @ApiOperation({ summary: 'Get available communities for members to join' })
  async getAvailableCommunities(
    @Query() baseQueryCoreDto: BaseQueryCoreDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.getAvailableCommunities(
      baseQueryCoreDto,
      sessionData,
    );
  }

  @Get(':id/details')
  @ApiOperation({ summary: 'Get community details' })
  async getCommunityDetails(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.getCommunityDetails(communityId, sessionData);
  }

  @Get(':id/subscription-status')
  @ApiOperation({ summary: 'Check user subscription status for a community' })
  async checkSubscriptionStatus(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.communityService.checkSubscriptionStatus(
      communityId,
      sessionData,
    );
  }

  // Apple Subscription Management Endpoints
  @Post(':id/apple-subscription')
  @ApiOperation({
    summary: 'Create Apple subscription for community (Owner only)',
    description: 'Creates Apple subscription group and product for the community. Community will remain PENDING until Apple approval.'
  })
  async createAppleCommunitySubscription(
    @Param('id') communityId: string,
    @Body() dto: CreateAppleCommunitySubscriptionDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    // Override communityId from URL parameter
    dto.communityId = communityId;
    return this.communityService.createAppleCommunitySubscription(dto, sessionData);
  }

  @Post(':id/apple-review')
  @ApiOperation({
    summary: 'Submit community for Apple review (Owner only)',
    description: 'Submits the community Apple subscription for Apple review. Community status will change to APPLE_REVIEW_PENDING.'
  })
  async submitAppleCommunityForReview(
    @Param('id') communityId: string,
    @Body() dto: SubmitAppleCommunityForReviewDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    // Override communityId from URL parameter
    dto.communityId = communityId;
    return this.communityService.submitAppleCommunityForReview(dto, sessionData);
  }

  @Get(':id/apple-status')
  @ApiOperation({
    summary: 'Get Apple subscription status for community',
    description: 'Returns the current Apple subscription and review status for the community.'
  })
  async getAppleSubscriptionStatus(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    // This would return Apple subscription details, review status, etc.
    // For now, we'll return basic community info with Apple-related fields
    const community = await this.communityService.getCommunityDetails(communityId, sessionData);

    return {
      status: true,
      message: 'Apple subscription status retrieved',
      data: {
        communityId: community.community.id,
        communityStatus: community.community.communityStatus,
        appleProductId: community.community.appleProductId,
        hasAppleSubscription: !!community.community.appleProductId,
        isAppleReviewPending: community.community.communityStatus === 'PENDING', // Will be updated when schema is applied
        isAppleReviewRejected: false, // Will be updated when schema is applied
        isActive: community.community.communityStatus === 'ACTIVE',
      },
    };
  }
}
