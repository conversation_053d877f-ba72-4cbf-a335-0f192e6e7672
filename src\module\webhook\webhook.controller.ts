import {
  Controller,
  Post,
  Body,
  Param,
  UseGuards,
  Headers,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { WebhookService, GooglePlayWebhookData, AppleWebhookData } from './webhook.service';
import { UserLoginJwtGuard } from 'src/module/auth/guards/firebase-auth.guard';
import { GetUserSession } from 'src/shared/decorators/user-session.decorator';
import { UserSessionType } from 'src/shared/types/user-session.type';

@ApiTags('Webhooks')
@Controller('webhook')
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) {}

  @Post('google-play')
  @ApiOperation({ summary: 'Handle Google Play webhook notifications' })
  async handleGooglePlayWebhook(
    @Body() webhookData: GooglePlayWebhookData,
    @Headers() headers: any,
  ) {
    // In production, you should verify the webhook signature
    // using Google's public key
    return this.webhookService.handleGooglePlayWebhook(webhookData);
  }

  @Post('apple')
  @ApiOperation({ summary: 'Handle Apple App Store webhook notifications' })
  async handleAppleWebhook(
    @Body() webhookData: AppleWebhookData,
    @Headers() headers: any,
  ) {
    // In production, you should verify the webhook signature
    // using Apple's certificate
    return this.webhookService.handleAppleWebhook(webhookData);
  }

  @Post('community/:id/approve')
  @ApiOperation({ summary: 'Approve community and change status to ACTIVE (Admin only)' })
  @ApiBearerAuth()
  @UseGuards(UserLoginJwtGuard)
  async approveCommunity(
    @Param('id') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    // In production, you should check if user has admin privileges
    return this.webhookService.approveCommunity(communityId);
  }

  @Post('community/:id/reject')
  @ApiOperation({ summary: 'Reject community (Admin only)' })
  @ApiBearerAuth()
  @UseGuards(UserLoginJwtGuard)
  async rejectCommunity(
    @Param('id') communityId: string,
    @Body() body: { reason?: string },
    @GetUserSession() sessionData: UserSessionType,
  ) {
    // In production, you should check if user has admin privileges
    return this.webhookService.rejectCommunity(communityId, body.reason);
  }
}
