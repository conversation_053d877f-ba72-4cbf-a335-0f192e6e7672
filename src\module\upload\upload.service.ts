import { Injectable } from '@nestjs/common';
import { UploadFileDto } from './dto/upload.dto';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';

@Injectable()
export class UploadService {
  private readonly s3Client = new S3Client({
    region: process.env.AWS_REGION!,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
    },
  });

  async getPreSignedUrl(param: { uploadFileDto: UploadFileDto }) {
    const {
      uploadFileDto: { fileName, resourceType },
    } = param;

    const bucket = process.env.AWS_BUCKET!;
    const filePath = `${resourceType.toLowerCase()}/${fileName}`;

    const command = new PutObjectCommand({
      Bucket: bucket,
      Key: filePath,
      ACL: 'public-read',
    });

    const preSignedUrl = await getSignedUrl(this.s3Client, command, {
      expiresIn: 3600,
    });

    const outPutUrl = `https://${bucket}.s3.${process.env.AWS_REGION}.amazonaws.com/${filePath}`;

    return {
      preSignedUrl,
      outPutUrl,
    };
  }

  async removeFileFromS3(param: { uploadFileDto: UploadFileDto }) {
    const {
      uploadFileDto: { fileName, resourceType },
    } = param;

    const bucket = process.env.AWS_BUCKET!;
    const filePath = `${resourceType.toLowerCase()}/${fileName}`;

    const command = new DeleteObjectCommand({
      Bucket: bucket,
      Key: filePath,
    });

    try {
      const response = await this.s3Client.send(command);
      return response;
    } catch (error) {
      throw new Error(error.message);
    }
  }
}
