import { ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserLoginJwtStrategy } from '../strategies/user-login-jwt.strategy';
import { AuthService } from '../auth.service';

@Injectable()
export class UserLoginJwtGuard extends UserLoginJwtStrategy {
  constructor(
    private readonly reflector: Reflector,
    authService: AuthService,
  ) {
    super(authService);
  }

  canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.get<boolean>(
      'isPublic',
      context.getHandler(),
    );

    if (isPublic) {
      return true;
    }

    return super.validate(context.switchToHttp().getRequest());
  }
}
