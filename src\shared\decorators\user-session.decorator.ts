import {
  createParamDecorator,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { UserSessionType } from '../types/user-session.type';

import admin from 'firebase-admin';
import { authMessages } from '../keys/helper.key';
import { PrismaClient, STATUS } from '@prisma/client';

export const GetUserSession = createParamDecorator(
  async (data: any, ctx: ExecutionContext): Promise<UserSessionType> => {
    const request = ctx.switchToHttp().getRequest();
    const headers = request.headers;

    if (!headers.authorization) {
      throw new UnauthorizedException(authMessages.AUTH_HEADER_NOT_FOUND);
    }

    const authHeaderValue = headers.authorization;

    if (!authHeaderValue.startsWith('Bearer')) {
      throw new UnauthorizedException(authMessages.AUTH_HEADER_IS_NOT_BEARER);
    }

    const parts = authHeaderValue.split(' ');

    if (parts.length !== 2) {
      throw new UnauthorizedException(authMessages.INVALID_AUTH_HEADER_BEARER);
    }

    const token = parts[1];

    const prisma = new PrismaClient();

    try {
      const decodedToken = await admin.auth().verifyIdToken(token);

      const user = await prisma.user.findUniqueOrThrow({
        where: {
          firebaseUid: decodedToken.uid,
          isDeleted: false,
        },
      });

      if (!user) {
        throw new UnauthorizedException(authMessages.USER_NOT_FOUND);
      }

      if (user.status === STATUS.DISABLED || user.isDeleted) {
        throw new UnauthorizedException(authMessages.USER_NOT_FOUND);
      }

      return {
        user,
      };
    } catch (error) {
      throw new UnauthorizedException(authMessages.TOKEN_EXPIRED);
    } finally {
      await prisma.$disconnect();
    }
  },
);

export const GetUserRequestHeader = createParamDecorator(
  (data: any, ctx: ExecutionContext): UserSessionType => {
    const request = ctx.switchToHttp().getRequest();
    return request.headers;
  },
);
