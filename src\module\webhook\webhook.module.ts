import { Module } from '@nestjs/common';
import { WebhookController } from './webhook.controller';
import { WebhookService } from './webhook.service';
import { CommunityCoreModule } from 'src/core/community-core/community-core.module';
import { MemberPurchaseCoreModule } from 'src/core/member-purchase-core/member-purchase-core.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    CommunityCoreModule,
    MemberPurchaseCoreModule,
    AuthModule,
  ],
  controllers: [WebhookController],
  providers: [WebhookService],
  exports: [WebhookService],
})
export class WebhookModule {}
