import { Global, Module } from '@nestjs/common';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { PriceModelCoreService } from './price-model-core.service';
import { PrismaModule } from 'src/shared/module/prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [PriceModelCoreService, PrismaService],
  exports: [PriceModelCoreService],
})
export class PriceModelCoreModule {}
