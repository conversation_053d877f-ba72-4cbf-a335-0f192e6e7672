import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { CommunityCoreService } from 'src/core/community-core/community-core.service';
import { MemberPurchaseCoreService } from 'src/core/member-purchase-core/member-purchase-core.service';
import { COMMUNITY_STATUS, MEMBERSHIP_STATUS } from '@prisma/client';
import { communityMessage } from 'src/shared/keys/helper.key';
import { CommunityService } from '../community/community.service';

export interface GooglePlayWebhookData {
  version: string;
  packageName: string;
  eventTimeMillis: string;
  subscriptionNotification?: {
    version: string;
    notificationType: number;
    purchaseToken: string;
    subscriptionId: string;
  };
}

export interface AppleWebhookData {
  notification_type: string;
  environment: string;
  latest_receipt_info?: {
    product_id: string;
    transaction_id: string;
    expires_date_ms: string;
  }[];
}

export interface AppleAppStoreConnectWebhookData {
  eventType: string;
  eventTime: string;
  data: {
    type: string;
    id: string;
    attributes: {
      state?: string;
      productId?: string;
      referenceName?: string;
      reviewSubmissionState?: string;
      platform?: string;
    };
    relationships?: {
      subscriptionGroup?: {
        data: {
          type: string;
          id: string;
        };
      };
    };
  };
}

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);

  constructor(
    private readonly communityCoreService: CommunityCoreService,
    private readonly memberPurchaseCoreService: MemberPurchaseCoreService,
    private readonly communityService: CommunityService,
  ) {}

  async handleGooglePlayWebhook(webhookData: GooglePlayWebhookData) {
    try {
      this.logger.log(
        'Processing Google Play webhook:',
        JSON.stringify(webhookData),
      );
      console.log('[WebhookService] Raw webhook data:', webhookData);

      if (!webhookData || !webhookData.subscriptionNotification) {
        this.logger.error(
          '[WebhookService] Invalid webhook payload',
          webhookData,
        );
        return;
      }

      const { subscriptionNotification } = webhookData;
      if (!subscriptionNotification) {
        throw new BadRequestException('Invalid webhook data');
      }

      const { notificationType, subscriptionId, purchaseToken } =
        subscriptionNotification;

      // Find community by Google product ID
      const community = await this.communityCoreService.findFirst({
        where: { googleProductId: subscriptionId },
      });

      if (!community) {
        this.logger.warn(
          `Community not found for product ID: ${subscriptionId}`,
        );
        return { status: false, message: 'Community not found' };
      }

      // Handle different notification types
      switch (notificationType) {
        case 1: // SUBSCRIPTION_RECOVERED
        case 2: // SUBSCRIPTION_RENEWED
          await this.handleSubscriptionRenewal(community.id, purchaseToken);
          break;
        case 3: // SUBSCRIPTION_CANCELED
        case 12: // SUBSCRIPTION_REVOKED
          await this.handleSubscriptionCancellation(
            community.id,
            purchaseToken,
          );
          break;
        case 4: // SUBSCRIPTION_PURCHASED
          await this.handleNewSubscription(
            community.id,
            purchaseToken,
            'android',
          );
          break;
        case 13: // SUBSCRIPTION_EXPIRED
          await this.handleSubscriptionExpiration(community.id, purchaseToken);
          break;
        default:
          this.logger.warn(`Unhandled notification type: ${notificationType}`);
      }

      return { status: true, message: 'Webhook processed successfully' };
    } catch (error) {
      this.logger.error('Error processing Google Play webhook:', error);
      throw new BadRequestException('Failed to process webhook');
    }
  }

  async handleAppleWebhook(webhookData: AppleWebhookData) {
    try {
      this.logger.log('Processing Apple webhook:', JSON.stringify(webhookData));

      const { notification_type, latest_receipt_info } = webhookData;
      if (!latest_receipt_info || latest_receipt_info.length === 0) {
        throw new BadRequestException('Invalid webhook data');
      }

      const receiptInfo = latest_receipt_info[0];
      const { product_id, transaction_id } = receiptInfo;

      // Find community by Apple product ID
      const community = await this.communityCoreService.findFirst({
        where: { appleProductId: product_id },
      });

      if (!community) {
        this.logger.warn(`Community not found for product ID: ${product_id}`);
        return { status: false, message: 'Community not found' };
      }

      // Handle different notification types
      switch (notification_type) {
        case 'INITIAL_BUY':
          await this.handleNewSubscription(community.id, transaction_id, 'ios');
          break;
        case 'RENEWAL':
          await this.handleSubscriptionRenewal(community.id, transaction_id);
          break;
        case 'CANCEL':
        case 'REFUND':
          await this.handleSubscriptionCancellation(
            community.id,
            transaction_id,
          );
          break;
        case 'DID_FAIL_TO_RENEW':
          await this.handleSubscriptionExpiration(community.id, transaction_id);
          break;
        default:
          this.logger.warn(`Unhandled notification type: ${notification_type}`);
      }

      return { status: true, message: 'Webhook processed successfully' };
    } catch (error) {
      this.logger.error('Error processing Apple webhook:', error);
      throw new BadRequestException('Failed to process webhook');
    }
  }

  async handleAppleAppStoreConnectWebhook(webhookData: AppleAppStoreConnectWebhookData) {
    try {
      this.logger.log('Processing Apple App Store Connect webhook:', JSON.stringify(webhookData));

      const { eventType, data } = webhookData;

      if (!data || !data.attributes) {
        throw new BadRequestException('Invalid webhook data');
      }

      // Handle different event types
      switch (eventType) {
        case 'SUBSCRIPTION_APPROVED':
          await this.handleAppleSubscriptionApproved(data);
          break;
        case 'SUBSCRIPTION_REJECTED':
          await this.handleAppleSubscriptionRejected(data);
          break;
        case 'REVIEW_SUBMISSION_APPROVED':
          await this.handleAppleReviewSubmissionApproved(data);
          break;
        case 'REVIEW_SUBMISSION_REJECTED':
          await this.handleAppleReviewSubmissionRejected(data);
          break;
        case 'SUBSCRIPTION_STATE_CHANGED':
          await this.handleAppleSubscriptionStateChanged(data);
          break;
        default:
          this.logger.warn(`Unhandled Apple App Store Connect event type: ${eventType}`);
      }

      return { status: true, message: 'Apple App Store Connect webhook processed successfully' };
    } catch (error) {
      this.logger.error('Error processing Apple App Store Connect webhook:', error);
      throw new BadRequestException('Failed to process Apple App Store Connect webhook');
    }
  }

  private async handleAppleSubscriptionApproved(data: any) {
    const productId = data.attributes.productId;
    if (!productId) {
      this.logger.warn('No product ID in Apple subscription approved webhook');
      return;
    }

    // Find community by Apple product ID
    const community = await this.communityCoreService.findFirst({
      where: { appleProductId: productId },
    });

    if (!community) {
      this.logger.warn(`Community not found for Apple product ID: ${productId}`);
      return;
    }

    // Update community status to ACTIVE
    await this.communityService.handleAppleReviewApproval(community.id);
    this.logger.log(`Apple subscription approved for community: ${community.id}`);
  }

  private async handleAppleSubscriptionRejected(data: any) {
    const productId = data.attributes.productId;
    if (!productId) {
      this.logger.warn('No product ID in Apple subscription rejected webhook');
      return;
    }

    // Find community by Apple product ID
    const community = await this.communityCoreService.findFirst({
      where: { appleProductId: productId },
    });

    if (!community) {
      this.logger.warn(`Community not found for Apple product ID: ${productId}`);
      return;
    }

    // Update community status to REJECTED
    const rejectionNote = data.attributes.rejectionReason || 'Subscription rejected by Apple';
    await this.communityService.handleAppleReviewRejection(community.id, rejectionNote);
    this.logger.log(`Apple subscription rejected for community: ${community.id}`);
  }

  private async handleAppleReviewSubmissionApproved(data: any) {
    this.logger.log('Apple review submission approved:', data.id);

    // Find communities with this review submission ID
    const communities = await this.communityCoreService.findMany({
      where: {
        // appleReviewSubmissionId: data.id
        // Temporarily using a different approach since schema isn't updated
        communityStatus: COMMUNITY_STATUS.PENDING
      },
    });

    for (const community of communities) {
      if (community.appleProductId) {
        await this.communityService.handleAppleReviewApproval(community.id);
        this.logger.log(`Review submission approved for community: ${community.id}`);
      }
    }
  }

  private async handleAppleReviewSubmissionRejected(data: any) {
    this.logger.log('Apple review submission rejected:', data.id);

    // Find communities with this review submission ID
    const communities = await this.communityCoreService.findMany({
      where: {
        // appleReviewSubmissionId: data.id
        // Temporarily using a different approach since schema isn't updated
        communityStatus: COMMUNITY_STATUS.PENDING
      },
    });

    const rejectionNote = data.attributes.rejectionReason || 'Review submission rejected by Apple';
    for (const community of communities) {
      if (community.appleProductId) {
        await this.communityService.handleAppleReviewRejection(community.id, rejectionNote);
        this.logger.log(`Review submission rejected for community: ${community.id}`);
      }
    }
  }

  private async handleAppleSubscriptionStateChanged(data: any) {
    const productId = data.attributes.productId;
    const newState = data.attributes.state;

    if (!productId || !newState) {
      this.logger.warn('Missing product ID or state in Apple subscription state changed webhook');
      return;
    }

    // Find community by Apple product ID
    const community = await this.communityCoreService.findFirst({
      where: { appleProductId: productId },
    });

    if (!community) {
      this.logger.warn(`Community not found for Apple product ID: ${productId}`);
      return;
    }

    this.logger.log(`Apple subscription state changed for community ${community.id}: ${newState}`);

    // Handle state changes
    switch (newState) {
      case 'READY_FOR_SALE':
        await this.communityService.handleAppleReviewApproval(community.id);
        break;
      case 'REJECTED':
        const rejectionNote = data.attributes.rejectionReason || 'Subscription rejected by Apple';
        await this.communityService.handleAppleReviewRejection(community.id, rejectionNote);
        break;
      case 'IN_REVIEW':
        // Update status to indicate it's under review
        await this.communityCoreService.update({
          where: { id: community.id },
          data: { communityStatus: COMMUNITY_STATUS.PENDING },
        });
        break;
      default:
        this.logger.log(`Unhandled Apple subscription state: ${newState}`);
    }
  }

  async approveCommunity(communityId: string) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    if (community.communityStatus !== COMMUNITY_STATUS.PENDING) {
      throw new BadRequestException('Community is not in pending status');
    }

    const updatedCommunity = await this.communityCoreService.update({
      where: { id: communityId },
      data: { communityStatus: COMMUNITY_STATUS.ACTIVE },
    });

    this.logger.log(`Community ${communityId} approved and activated`);

    return {
      status: true,
      message: communityMessage.COMMUNITY_STATUS_UPDATED,
      community: updatedCommunity,
    };
  }

  async rejectCommunity(communityId: string, reason?: string) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    if (community.communityStatus !== COMMUNITY_STATUS.PENDING) {
      throw new BadRequestException('Community is not in pending status');
    }

    // For now, we'll just keep it as PENDING with a rejection reason
    // In a real app, you might want to add a REJECTED status
    this.logger.log(`Community ${communityId} rejected. Reason: ${reason}`);

    return {
      status: true,
      message: 'Community rejected',
      reason,
    };
  }

  private async handleNewSubscription(
    communityId: string,
    token: string,
    platform: 'android' | 'ios',
  ) {
    // This would typically be handled by the join community flow
    // But we can log it for tracking purposes
    this.logger.log(
      `New subscription for community ${communityId} on ${platform}`,
    );
  }

  private async handleSubscriptionRenewal(communityId: string, token: string) {
    // Find and update the purchase record
    const purchase = await this.memberPurchaseCoreService.findFirst({
      where: { communityId, id: token }, // Using token as purchase ID for simplicity
    });

    if (purchase) {
      await this.memberPurchaseCoreService.update({
        where: { id: purchase.id },
        data: { membershipStatus: MEMBERSHIP_STATUS.ACTIVE },
      });
      this.logger.log(`Subscription renewed for purchase ${purchase.id}`);
    }
  }

  private async handleSubscriptionCancellation(
    communityId: string,
    token: string,
  ) {
    const purchase = await this.memberPurchaseCoreService.findFirst({
      where: { communityId, id: token },
    });

    if (purchase) {
      await this.memberPurchaseCoreService.update({
        where: { id: purchase.id },
        data: { membershipStatus: MEMBERSHIP_STATUS.REVOKED },
      });
      this.logger.log(`Subscription cancelled for purchase ${purchase.id}`);
    }
  }

  private async handleSubscriptionExpiration(
    communityId: string,
    token: string,
  ) {
    const purchase = await this.memberPurchaseCoreService.findFirst({
      where: { communityId, id: token },
    });

    if (purchase) {
      await this.memberPurchaseCoreService.update({
        where: { id: purchase.id },
        data: { membershipStatus: MEMBERSHIP_STATUS.EXPIRED },
      });
      this.logger.log(`Subscription expired for purchase ${purchase.id}`);
    }
  }
}
