import {
  Injectable,
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { USER_TYPE, STATUS } from '@prisma/client';
import {
  CreateCertificateDto,
  UpdateOwnerProfileDto,
  UpdateMemberProfileDto,
  UpdateNotificationPreferenceDto,
} from './dto/profile.dto';
import { certificateMessage, userMessage } from 'src/shared/keys/helper.key';
import { UserCoreService } from 'src/core/user-core/user-core.service';
import { CertificateCoreService } from 'src/core/certificate-core/certificate-core.service';
import { UserSessionType } from 'src/shared/types/user-session.type';

@Injectable()
export class ProfileService {
  constructor(
    private readonly userCoreService: UserCoreService,
    private readonly certificateCoreService: CertificateCoreService,
  ) {}

  // Update member profile
  async updateMemberProfile(
    userId: string,
    dto: UpdateMemberProfileDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.MEMBER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    await this.userCoreService.update({
      where: { id: userId },
      data: dto,
    });

    const updatedUser = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    return {
      status: true,
      message: userMessage.PROFILE_UPDATED,
      user: updatedUser,
    };
  }

  // Update owner profile (only bio and profileImage)
  async updateOwnerProfile(
    userId: string,
    dto: UpdateOwnerProfileDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });
    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    await this.userCoreService.update({
      where: { id: userId },
      data: dto,
    });

    const updatedUser = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    return {
      status: true,
      message: userMessage.PROFILE_UPDATED,
      user: updatedUser,
    };
  }

  // Add certificate
  async addCertificate(
    userId: string,
    dto: CreateCertificateDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    await this.certificateCoreService.create({
      data: {
        ...dto,
        userId,
      },
    });

    return {
      status: true,
      message: certificateMessage.CERTIFICATE_ADDED,
    };
  }

  // Delete certificate
  async deleteCertificate(
    userId: string,
    certificateId: string,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const cert = await this.certificateCoreService.findUnique({
      where: { id: certificateId },
    });

    if (!cert || cert.userId !== userId) {
      throw new NotFoundException(certificateMessage.CERTIFICATE_NOT_FOUND);
    }

    if (cert.isDeleted) {
      throw new BadRequestException(
        certificateMessage.CERTIFICATE_ALREADY_DELETED,
      );
    }

    await this.certificateCoreService.update({
      where: { id: certificateId },
      data: {
        isDeleted: true,
        status: STATUS.DISABLED,
      },
    });

    return {
      status: true,
      message: certificateMessage.CERTIFICATE_DELETED,
    };
  }

  // Get all certificates of a user
  async getCertificates(userId: string, sessionData: UserSessionType) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const certificates = await this.certificateCoreService.findMany({
      where: {
        userId,
        isDeleted: false,
        status: STATUS.ENABLED,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return {
      status: true,
      data: certificates,
    };
  }

  async updateNotificationPreference(
    userId: string,
    dto: UpdateNotificationPreferenceDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.MEMBER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    await this.userCoreService.update({
      where: { id: userId },
      data: {
        isPushNotificationsEnabled: dto.isPushNotificationsEnabled,
      },
    });

    return {
      status: true,
      message: 'Push notification preference updated',
    };
  }
}
