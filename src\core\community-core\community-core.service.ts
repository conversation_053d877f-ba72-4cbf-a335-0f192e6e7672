import { Injectable } from '@nestjs/common';
import { Community, Prisma } from '@prisma/client';
import { PrismaBaseRepository } from 'src/shared/libs/prisma-base.repository';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { CommunityCorePaginateDto } from './dto/community-core.dto';
import { communityMessage } from 'src/shared/keys/helper.key';

@Injectable()
export class CommunityCoreService extends PrismaBaseRepository<
  Community,
  CommunityCorePaginateDto,
  Prisma.CommunityCreateArgs,
  Prisma.CommunityUpdateArgs,
  Prisma.CommunityUpdateManyArgs,
  Prisma.CommunityFindUniqueArgs,
  Prisma.CommunityFindFirstArgs,
  Prisma.CommunityFindManyArgs,
  Prisma.CommunityDeleteArgs,
  Prisma.CommunityDeleteManyArgs,
  Prisma.CommunityCountArgs
> {
  constructor(public prisma: PrismaService) {
    super(prisma.prisma.community, {
      NOT_FOUND: communityMessage.COMMUNITY_NOT_FOUND,
      DELETED: communityMessage.COMMUNITY_IS_DELETED,
    });
  }
}
