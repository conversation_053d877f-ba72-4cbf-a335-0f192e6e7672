import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsString, ValidateIf } from 'class-validator';

export enum FILE_TYPE {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  FILE = 'FILE',
}

export enum RESOURCE_TYPE {
  USER = 'USER',
  OWNER = 'OWNER',
}

export class UploadFileDto {
  @ApiProperty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({ enum: RESOURCE_TYPE })
  @ValidateIf((o) => o.type === RESOURCE_TYPE.USER)
  @IsEnum(RESOURCE_TYPE)
  resourceType: RESOURCE_TYPE;
}
