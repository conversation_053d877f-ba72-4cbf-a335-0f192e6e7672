import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { MemberPurchaseCoreService } from 'src/core/member-purchase-core/member-purchase-core.service';
import { CommunityCoreService } from 'src/core/community-core/community-core.service';
import { IAPService } from 'src/module/iap/iap.service';
import { UserSessionType } from 'src/shared/types/user-session.type';
import {
  memberPurchaseMessage,
  communityMessage,
} from 'src/shared/keys/helper.key';
import { MEMBERSHIP_STATUS } from '@prisma/client';

@Injectable()
export class SubscriptionService {
  constructor(
    private readonly memberPurchaseCoreService: MemberPurchaseCoreService,
    private readonly communityCoreService: CommunityCoreService,
    private readonly iapService: IAPService,
  ) {}

  async renewSubscription(
    communityId: string,
    receiptData: string,
    platform: 'android' | 'ios',
    sessionData: UserSessionType,
    packageName?: string,
  ) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    if (!community) {
      throw new NotFoundException(communityMessage.COMMUNITY_NOT_FOUND);
    }

    // Find existing purchase
    const existingPurchase =
      await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
        sessionData.user.id,
        communityId,
      );

    if (!existingPurchase) {
      throw new BadRequestException(memberPurchaseMessage.PURCHASE_NOT_FOUND);
    }

    // Validate new receipt
    const validationResult = await this.validateReceipt(
      platform,
      community.googleProductId || community.appleProductId || '',
      receiptData,
      packageName,
    );

    if (!validationResult.isValid) {
      throw new BadRequestException(memberPurchaseMessage.PURCHASE_INVALID);
    }

    // Update purchase with new expiration date
    const updatedPurchase = await this.memberPurchaseCoreService.update({
      where: { id: existingPurchase.id },
      data: {
        expiresAt: validationResult.expirationDate,
        membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
      },
    });

    return {
      status: true,
      message: 'Subscription renewed successfully',
      purchase: updatedPurchase,
    };
  }

  async cancelSubscription(communityId: string, sessionData: UserSessionType) {
    const purchase =
      await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
        sessionData.user.id,
        communityId,
      );

    if (!purchase) {
      throw new NotFoundException(memberPurchaseMessage.PURCHASE_NOT_FOUND);
    }

    // Update purchase status to cancelled
    const cancelledPurchase = await this.memberPurchaseCoreService.update({
      where: { id: purchase.id },
      data: { membershipStatus: MEMBERSHIP_STATUS.REVOKED },
    });

    return {
      status: true,
      message: 'Subscription cancelled successfully',
      purchase: cancelledPurchase,
    };
  }

  async checkExpiredSubscriptions() {
    const expiredPurchases = await this.memberPurchaseCoreService.findMany({
      where: {
        membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
        expiresAt: {
          lt: new Date(),
        },
      },
    });

    const updatedPurchases: any = [];

    for (const purchase of expiredPurchases) {
      const updated = await this.memberPurchaseCoreService.update({
        where: { id: purchase.id },
        data: { membershipStatus: MEMBERSHIP_STATUS.EXPIRED },
      });
      updatedPurchases.push(updated);
    }

    return {
      status: true,
      message: `Updated ${updatedPurchases.length} expired subscriptions`,
      updatedCount: updatedPurchases.length,
    };
  }

  async validateAndSyncSubscription(
    communityId: string,
    sessionData: UserSessionType,
  ) {
    const purchase =
      await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
        sessionData.user.id,
        communityId,
      );

    if (!purchase) {
      throw new NotFoundException(memberPurchaseMessage.PURCHASE_NOT_FOUND);
    }

    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    // Check subscription status with app store
    const subscriptionStatus = await this.iapService.checkSubscriptionStatus(
      purchase.platform as 'android' | 'ios',
      process.env.GOOGLE_PLAY_PACKAGE_NAME || '',
      purchase.productId,
      purchase.id, // Using purchase ID as token placeholder
    );

    // Update local status based on app store response
    if (
      !subscriptionStatus.isActive &&
      purchase.membershipStatus === MEMBERSHIP_STATUS.ACTIVE
    ) {
      await this.memberPurchaseCoreService.update({
        where: { id: purchase.id },
        data: {
          membershipStatus: MEMBERSHIP_STATUS.EXPIRED,
          expiresAt: subscriptionStatus.expirationDate,
        },
      });
    } else if (
      subscriptionStatus.isActive &&
      purchase.membershipStatus !== MEMBERSHIP_STATUS.ACTIVE
    ) {
      await this.memberPurchaseCoreService.update({
        where: { id: purchase.id },
        data: {
          membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
          expiresAt: subscriptionStatus.expirationDate,
        },
      });
    }

    return {
      status: true,
      isActive: subscriptionStatus.isActive,
      expirationDate: subscriptionStatus.expirationDate,
      purchase,
    };
  }

  async getUserSubscriptions(sessionData: UserSessionType) {
    const purchases = await this.memberPurchaseCoreService.findPurchasesByUser(
      sessionData.user.id,
    );

    // Separate active and inactive subscriptions
    const activeSubscriptions = purchases.filter(
      (p) =>
        p.membershipStatus === MEMBERSHIP_STATUS.ACTIVE &&
        (!p.expiresAt || p.expiresAt > new Date()),
    );
    const inactiveSubscriptions = purchases.filter(
      (p) => p.membershipStatus !== MEMBERSHIP_STATUS.ACTIVE,
    );

    return {
      status: true,
      activeSubscriptions,
      inactiveSubscriptions,
      totalCount: purchases.length,
    };
  }

  private async validateReceipt(
    platform: 'android' | 'ios',
    productId: string,
    receiptData: string,
    packageName?: string,
  ) {
    if (platform === 'android') {
      return this.iapService.validateGooglePlayReceipt(
        packageName || '',
        productId,
        receiptData,
      );
    } else {
      return this.iapService.validateAppleReceipt(receiptData);
    }
  }
}
