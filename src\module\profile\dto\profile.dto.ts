import { IsOptional, IsString, IsDateString, IsUrl, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateMemberProfileDto {
  @ApiProperty()
  @IsString()
  firstName?: string;

  @ApiProperty()
  @IsString()
  lastName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  countryCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  contact?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  bio?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  profileImage?: string;
}

export class UpdateOwnerProfileDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  bio?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  profileImage?: string;
}

export class CreateCertificateDto {
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsUrl()
  url: string;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsString()
  // issuer: string;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsDateString()
  // issuedDate?: string;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsDateString()
  // expiryDate?: string;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsString()
  // description?: string;
}

export class UpdateNotificationPreferenceDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  isPushNotificationsEnabled: boolean;
}