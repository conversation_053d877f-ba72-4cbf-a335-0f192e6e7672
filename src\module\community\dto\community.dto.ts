import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsEnum,
  IsNotEmpty,
  Min,
} from 'class-validator';
import { COMMUNITY_STATUS, PLATFORM } from '@prisma/client';
import { AppleSubscriptionPeriod } from '../../iap/dto/apple-subscription.dto';

export class CreateCommunityDto {
  @ApiProperty({ description: 'Community name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Community description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Community image URL', required: false })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ description: 'Community banner URL', required: false })
  @IsString()
  @IsOptional()
  banner?: string;

  @ApiProperty({ description: 'Community video URL', required: false })
  @IsString()
  @IsOptional()
  videoUrl?: string;

  @ApiProperty({ description: 'Is community free', default: true })
  @IsBoolean()
  isFree: boolean;

  @ApiProperty({ description: 'Price amount (if not free)', required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  priceAmount?: number;

  @ApiProperty({
    description: 'Price currency',
    default: 'AUD',
    required: false,
  })
  @IsString()
  @IsOptional()
  priceCurrency?: string = 'AUD';
}

export class UpdateCommunityDto {
  @ApiProperty({ description: 'Community name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Community description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Community image URL', required: false })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ description: 'Community banner URL', required: false })
  @IsString()
  @IsOptional()
  banner?: string;

  @ApiProperty({ description: 'Community video URL', required: false })
  @IsString()
  @IsOptional()
  videoUrl?: string;

  // @ApiProperty({ description: 'Is community free', default: true })
  // @IsBoolean()
  // @IsNotEmpty()
  // isFree: boolean = true;

  // @ApiProperty({ description: 'Price amount (if not free)', required: false })
  // @IsNumber()
  // @IsOptional()
  // @Min(0)
  // priceAmount?: number;

  // @ApiProperty({
  //   description: 'Price currency',
  //   default: 'AUD',
  //   required: false,
  // })
  // @IsString()
  // @IsOptional()
  // priceCurrency?: string = 'AUD';
}

export class UpdateCommunityStatusDto {
  @ApiProperty({ description: 'Community status', enum: COMMUNITY_STATUS })
  @IsEnum(COMMUNITY_STATUS)
  @IsNotEmpty()
  communityStatus: COMMUNITY_STATUS;
}

export class CreateAppleCommunitySubscriptionDto {
  @ApiProperty({ description: 'Community ID' })
  @IsString()
  @IsNotEmpty()
  communityId: string;

  @ApiProperty({ description: 'Apple App ID from App Store Connect' })
  @IsString()
  @IsNotEmpty()
  appId: string;

  @ApiProperty({
    description: 'Subscription period',
    enum: AppleSubscriptionPeriod,
    default: AppleSubscriptionPeriod.ONE_MONTH
  })
  @IsEnum(AppleSubscriptionPeriod)
  @IsOptional()
  subscriptionPeriod?: AppleSubscriptionPeriod = AppleSubscriptionPeriod.ONE_MONTH;

  @ApiProperty({ description: 'Whether the subscription is family shareable', default: false })
  @IsBoolean()
  @IsOptional()
  familyShareable?: boolean = false;

  @ApiProperty({ description: 'Review note for Apple submission', required: false })
  @IsString()
  @IsOptional()
  reviewNote?: string;
}

export class SubmitAppleCommunityForReviewDto {
  @ApiProperty({ description: 'Community ID' })
  @IsString()
  @IsNotEmpty()
  communityId: string;

  @ApiProperty({ description: 'Review note for Apple submission', required: false })
  @IsString()
  @IsOptional()
  reviewNote?: string;

  @ApiProperty({ description: 'Community image for review', required: false })
  @IsString()
  @IsOptional()
  reviewImage?: string;
}

export class JoinCommunityDto {
  @ApiProperty({ description: 'Community ID' })
  @IsString()
  @IsNotEmpty()
  communityId: string;

  @ApiProperty({ description: 'Platform (android or ios)' })
  @IsEnum(PLATFORM)
  @IsNotEmpty()
  platform: PLATFORM;

  @ApiProperty({
    description: 'Purchase receipt/token (for paid communities)',
    required: false,
  })
  @IsString()
  @IsOptional()
  receiptData?: string;

  @ApiProperty({
    description: 'Package name (required for Android)',
    required: false,
  })
  @IsString()
  @IsOptional()
  packageName?: string;
}
