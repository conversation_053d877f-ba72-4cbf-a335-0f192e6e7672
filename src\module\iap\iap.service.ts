import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { google } from 'googleapis';
import * as appleReceiptVerify from 'node-apple-receipt-verify';

export enum SubscriptionProrationMode {
  SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE = 'SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE',
}

export enum ResubscribeState {
  RESUBSCRIBE_STATE_ACTIVE = 'RESUBSCRIBE_STATE_ACTIVE',
}

interface ConvertedPrice {
  regionCode: string;
  price: {
    currencyCode: string;
    units: string;
    nanos: number;
  };
  taxAmount?: any;
}

export interface AppleProduct {
  productId: string;
  bundleId: string;
  displayName: string;
  description: string;
  price: string;
  currency: string;
  subscriptionPeriod: string;
}

export interface ReceiptValidationResult {
  isValid: boolean;
  productId?: string;
  transactionId?: string;
  purchaseDate?: Date;
  expirationDate?: Date;
  platform: 'android' | 'ios';
  originalData?: any;
}

@Injectable()
export class IAPService {
  private readonly logger = new Logger(IAPService.name);
  private googlePlayPublisher: any;

  constructor(private configService: ConfigService) {
    this.initializeGooglePlay();
    this.initializeApple();
  }

  private async initializeGooglePlay() {
    try {
      const serviceAccountKey = this.configService.get(
        'GOOGLE_APPLICATION_CREDENTIALS',
      );
      if (serviceAccountKey) {
        const auth = new google.auth.GoogleAuth({
          keyFile: serviceAccountKey,
          scopes: ['https://www.googleapis.com/auth/androidpublisher'],
        });

        this.googlePlayPublisher = google.androidpublisher({
          version: 'v3',
          auth,
        });
      }
    } catch (error) {
      this.logger.error('Failed to initialize Google Play API:', error);
    }
  }

  private initializeApple() {
    try {
      const appleConfig = {
        secret: this.configService.get('APPLE_SHARED_SECRET'),
        environment: this.configService.get('APPLE_ENVIRONMENT') || 'sandbox',
        verbose: false,
        extended: true,
      };
      appleReceiptVerify.config(appleConfig);
    } catch (error) {
      this.logger.error(
        'Failed to initialize Apple receipt verification:',
        error,
      );
    }
  }

  async convertRegionalPrices(
    packageName: string,
    basePrice: number,
    baseCurrency: string = 'AUD',
  ): Promise<any> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      const requestBody = {
        price: {
          currencyCode: baseCurrency,
          units: Math.floor(basePrice).toString(),
          nanos: Math.round((basePrice % 1) * 1e9),
        },
      };

      const response =
        await this.googlePlayPublisher.monetization.convertRegionPrices({
          packageName,
          requestBody,
        });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to convert regional prices:', error);
      throw error;
    }
  }

  async createGooglePlayProduct(
    packageName: string,
    productId: string,
    price: number,
    currency: string,
    title: string,
  ): Promise<any> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      // Step 1: Convert regional prices first
      const priceConversionResponse = await this.convertRegionalPrices(
        packageName,
        price,
        currency,
      );

      // Step 2: Extract converted prices and create regional configs
      const regionalConfigs: any = [];

      if (
        priceConversionResponse.convertedRegionPrices &&
        typeof priceConversionResponse.convertedRegionPrices === 'object'
      ) {
        for (const convertedPrice of Object.values(
          priceConversionResponse.convertedRegionPrices,
        ) as ConvertedPrice[]) {
          if (convertedPrice.regionCode) {
            regionalConfigs.push({
              regionCode: convertedPrice.regionCode,
              price: convertedPrice.price,
            });
          }
        }
      }

      // Fallback: If conversion failed or no converted prices, use base region only
      if (regionalConfigs.length === 0) {
        regionalConfigs.push({
          regionCode: 'AU',
          price: {
            currencyCode: currency,
            units: Math.floor(price).toString(),
            nanos: Math.round((price % 1) * 1e9),
          },
        });
      }

      const safeProductId = productId.replace(/\./g, '-');
      const basePlanId = `${safeProductId}-base-plan`;

      const requestBody = {
        basePlans: [
          {
            basePlanId,
            state: 'ACTIVE',
            regionalConfigs,
            autoRenewingBasePlanType: {
              billingPeriodDuration: 'P1M',
              gracePeriodDuration: 'P7D',
              accountHoldDuration: 'P30D',
              resubscribeState: ResubscribeState.RESUBSCRIBE_STATE_ACTIVE,
              prorationMode:
                SubscriptionProrationMode.SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE,
            },
          },
        ],
        listings: {
          languageCode: 'en-US',
          title: `${title} Plan`,
        },
      };

      // Step 3: Create the product with all regional pricing
      const response =
        await this.googlePlayPublisher.monetization.subscriptions.create({
          packageName,
          productId,
          'regionsVersion.version': '2025/01',
          requestBody,
        });

      // Step 4: Activate the base plan
      await this.googlePlayPublisher.monetization.subscriptions.basePlans.activate(
        {
          packageName,
          productId,
          basePlanId,
        },
      );

      return {
        subscription: response.data,
        regionalPricing: regionalConfigs,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create Google Play product ${productId}:`,
        error,
      );
      throw error;
    }
  }

  async updateGooglePlayProduct(
    packageName: string,
    productId: string,
    // price: number,
    // currency: string,
    title: string,
  ): Promise<any> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      // Step 1: Convert regional prices
      // const priceConversionResponse = await this.convertRegionalPrices(
      //   packageName,
      //   price,
      //   currency,
      // );

      // const regionalConfigs: any[] = [];

      // if (
      //   priceConversionResponse.convertedRegionPrices &&
      //   typeof priceConversionResponse.convertedRegionPrices === 'object'
      // ) {
      //   for (const convertedPrice of Object.values(
      //     priceConversionResponse.convertedRegionPrices,
      //   ) as ConvertedPrice[]) {
      //     if (convertedPrice.regionCode) {
      //       regionalConfigs.push({
      //         regionCode: convertedPrice.regionCode,
      //         price: convertedPrice.price,
      //       });
      //     }
      //   }
      // }

      // Fallback to base price if conversions fail
      // if (regionalConfigs.length === 0) {
      //   regionalConfigs.push({
      //     regionCode: 'AU',
      //     price: {
      //       currencyCode: currency,
      //       units: Math.floor(price).toString(),
      //       nanos: Math.round((price % 1) * 1e9),
      //     },
      //   });
      // }

      // const safeProductId = productId.replace(/\./g, '-');
      // const basePlanId = `${safeProductId}-base-plan`;

      const requestBody = {
        listings: {
          languageCode: 'en-US',
          title: `${title} Plan`,
        },
        // basePlans: [
        //   {
        //     basePlanId,
        //     state: 'ACTIVE',
        //     regionalConfigs,
        //     autoRenewingBasePlanType: {
        //       billingPeriodDuration: 'P1M',
        //       gracePeriodDuration: 'P7D',
        //       accountHoldDuration: 'P30D',
        //       resubscribeState: ResubscribeState.RESUBSCRIBE_STATE_ACTIVE,
        //       prorationMode:
        //         SubscriptionProrationMode.SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE,
        //     },
        //   },
        // ],
      };

      const response =
        await this.googlePlayPublisher.monetization.subscriptions.patch({
          packageName,
          productId,
          updateMask: 'listings', // Add here if base plan is updated
          'regionsVersion.version': '2025/01',
          requestBody,
        });

      return {
        subscription: response.data,
        // regionalPricing: regionalConfigs,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update Google Play product ${productId}:`,
        error,
      );
      throw error;
    }
  }

  async deleteGooglePlayProduct(
    packageName: string,
    productId: string,
  ): Promise<void> {
    if (!this.googlePlayPublisher) {
      throw new Error('Google Play API not initialized');
    }

    await this.googlePlayPublisher.monetization.subscriptions.delete({
      packageName,
      productId,
    });
  }

  async createAppleProduct(
    bundleId: string,
    productId: string,
    displayName: string,
    description: string,
    price: string,
    currency: string,
  ): Promise<AppleProduct | null> {
    try {
      // Create Apple In-App Purchase using App Store Connect API
      const appleApiKey = this.configService.get('APPLE_API_KEY');
      const appleKeyId = this.configService.get('APPLE_KEY_ID');
      const appleIssuerId = this.configService.get('APPLE_ISSUER_ID');

      if (!appleApiKey || !appleKeyId || !appleIssuerId) {
        this.logger.warn(
          'Apple API credentials not configured, creating mock product',
        );
        const mockProduct: AppleProduct = {
          productId,
          bundleId,
          displayName,
          description,
          price,
          currency,
          subscriptionPeriod: 'P1M',
        };
        this.logger.log(`Apple product structure created: ${productId}`);
        return mockProduct;
      }

      // App Store Connect API request body
      const requestBody = {
        data: {
          type: 'inAppPurchases',
          attributes: {
            productId: productId,
            referenceName: displayName,
            inAppPurchaseType: 'AUTOMATICALLY_RENEWABLE_SUBSCRIPTION',
            state: 'DEVELOPER_ACTION_NEEDED',
          },
          relationships: {
            app: {
              data: {
                type: 'apps',
                id: bundleId, // This should be the app ID from App Store Connect
              },
            },
          },
        },
      };

      // Note: In production, you would make an actual API call to App Store Connect
      // For now, we'll simulate the creation
      this.logger.log(
        `Apple product would be created via App Store Connect API: ${productId}`,
      );

      const appleProduct: AppleProduct = {
        productId,
        bundleId,
        displayName,
        description,
        price,
        currency,
        subscriptionPeriod: 'P1M',
      };

      this.logger.log(
        `Apple product structure created: ${productId}`,
        appleProduct,
      );
      return appleProduct;
    } catch (error) {
      this.logger.error(`Failed to create Apple product ${productId}:`, error);
      return null;
    }
  }

  async validateGooglePlayReceipt(
    packageName: string,
    productId: string,
    purchaseToken: string,
  ): Promise<ReceiptValidationResult> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      const response =
        await this.googlePlayPublisher.purchases.subscriptions.get({
          packageName: packageName,
          subscriptionId: productId,
          token: purchaseToken,
        });

      const purchase = response.data;
      const isValid = purchase.paymentState === 1; // 1 = Received
      const expirationDate = purchase.expiryTimeMillis
        ? new Date(parseInt(purchase.expiryTimeMillis))
        : undefined;

      return {
        isValid,
        productId,
        transactionId: purchase.orderId,
        purchaseDate: purchase.startTimeMillis
          ? new Date(parseInt(purchase.startTimeMillis))
          : undefined,
        expirationDate,
        platform: 'android',
        originalData: purchase,
      };
    } catch (error) {
      this.logger.error('Google Play receipt validation failed:', error);
      return {
        isValid: false,
        platform: 'android',
      };
    }
  }

  async validateAppleReceipt(
    receiptData: string,
  ): Promise<ReceiptValidationResult> {
    try {
      const result = await appleReceiptVerify.validate({
        receipt: receiptData,
        device: false,
      });

      if (result && result.receipt) {
        const latestReceiptInfo =
          result.latest_receipt_info?.[0] || result.receipt.in_app?.[0];

        return {
          isValid: true,
          productId: latestReceiptInfo?.product_id,
          transactionId: latestReceiptInfo?.transaction_id,
          purchaseDate: latestReceiptInfo?.purchase_date_ms
            ? new Date(parseInt(latestReceiptInfo.purchase_date_ms))
            : undefined,
          expirationDate: latestReceiptInfo?.expires_date_ms
            ? new Date(parseInt(latestReceiptInfo.expires_date_ms))
            : undefined,
          platform: 'ios',
          originalData: result,
        };
      }

      return {
        isValid: false,
        platform: 'ios',
      };
    } catch (error) {
      this.logger.error('Apple receipt validation failed:', error);
      return {
        isValid: false,
        platform: 'ios',
      };
    }
  }

  async checkSubscriptionStatus(
    platform: 'android' | 'ios',
    packageName: string,
    productId: string,
    token: string,
  ): Promise<{ isActive: boolean; expirationDate?: Date }> {
    try {
      if (platform === 'android') {
        const result = await this.validateGooglePlayReceipt(
          packageName,
          productId,
          token,
        );
        return {
          isActive:
            result.isValid && result.expirationDate
              ? result.expirationDate > new Date()
              : false,
          expirationDate: result.expirationDate,
        };
      } else {
        const result = await this.validateAppleReceipt(token);
        return {
          isActive:
            result.isValid && result.expirationDate
              ? result.expirationDate > new Date()
              : false,
          expirationDate: result.expirationDate,
        };
      }
    } catch (error) {
      this.logger.error('Subscription status check failed:', error);
      return { isActive: false };
    }
  }
}
