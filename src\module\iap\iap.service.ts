import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { google } from 'googleapis';
import * as appleReceiptVerify from 'node-apple-receipt-verify';
import * as jwt from 'jsonwebtoken';
import axios from 'axios';

export enum SubscriptionProrationMode {
  SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE = 'SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE',
}

export enum ResubscribeState {
  RESUBSCRIBE_STATE_ACTIVE = 'RESUBSCRIBE_STATE_ACTIVE',
}

interface ConvertedPrice {
  regionCode: string;
  price: {
    currencyCode: string;
    units: string;
    nanos: number;
  };
  taxAmount?: any;
}

export interface AppleProduct {
  productId: string;
  bundleId: string;
  displayName: string;
  description: string;
  price: string;
  currency: string;
  subscriptionPeriod: string;
}

export interface AppleSubscriptionGroup {
  id?: string;
  referenceName: string;
  appId: string;
  status?:
    | 'READY_FOR_SALE'
    | 'DEVELOPER_ACTION_NEEDED'
    | 'PENDING_DEVELOPER_RELEASE'
    | 'IN_REVIEW'
    | 'REJECTED';
}

export interface AppleSubscriptionProduct {
  id?: string;
  productId: string;
  referenceName: string;
  subscriptionGroupId: string;
  state?:
    | 'READY_FOR_SALE'
    | 'DEVELOPER_ACTION_NEEDED'
    | 'PENDING_DEVELOPER_RELEASE'
    | 'IN_REVIEW'
    | 'REJECTED';
  subscriptionPeriod:
    | 'ONE_WEEK'
    | 'ONE_MONTH'
    | 'TWO_MONTHS'
    | 'THREE_MONTHS'
    | 'SIX_MONTHS'
    | 'ONE_YEAR';
  reviewNote?: string;
  familyShareable?: boolean;
}

export interface AppleSubscriptionPrice {
  id?: string;
  subscriptionId: string;
  territory: string;
  preserveCurrentPrice?: boolean;
  startDate?: string;
  endDate?: string;
}

export interface AppleReviewSubmission {
  id?: string;
  platform: 'IOS';
  submittedDate?: string;
  state?:
    | 'READY_FOR_REVIEW'
    | 'IN_REVIEW'
    | 'PENDING_DEVELOPER_RELEASE'
    | 'APPROVED'
    | 'REJECTED';
  items: Array<{
    id: string;
    type: 'IN_APP_PURCHASE';
  }>;
}

export enum AppleSubscriptionState {
  READY_FOR_SALE = 'READY_FOR_SALE',
  DEVELOPER_ACTION_NEEDED = 'DEVELOPER_ACTION_NEEDED',
  PENDING_DEVELOPER_RELEASE = 'PENDING_DEVELOPER_RELEASE',
  IN_REVIEW = 'IN_REVIEW',
  REJECTED = 'REJECTED',
}

export interface ReceiptValidationResult {
  isValid: boolean;
  productId?: string;
  transactionId?: string;
  purchaseDate?: Date;
  expirationDate?: Date;
  platform: 'android' | 'ios';
  originalData?: any;
}

@Injectable()
export class IAPService {
  private readonly logger = new Logger(IAPService.name);
  private googlePlayPublisher: any;
  private appleApiClient: any;
  private readonly APPLE_API_BASE_URL =
    'https://api.appstoreconnect.apple.com/v1';

  constructor(private configService: ConfigService) {
    this.initializeGooglePlay();
    this.initializeApple();
    this.initializeAppleApiClient();
  }

  private async initializeGooglePlay() {
    try {
      const serviceAccountKey = this.configService.get(
        'GOOGLE_APPLICATION_CREDENTIALS',
      );
      if (serviceAccountKey) {
        const auth = new google.auth.GoogleAuth({
          keyFile: serviceAccountKey,
          scopes: ['https://www.googleapis.com/auth/androidpublisher'],
        });

        this.googlePlayPublisher = google.androidpublisher({
          version: 'v3',
          auth,
        });
      }
    } catch (error) {
      this.logger.error('Failed to initialize Google Play API:', error);
    }
  }

  private initializeApple() {
    try {
      const appleConfig = {
        secret: this.configService.get('APPLE_SHARED_SECRET'),
        environment: this.configService.get('APPLE_ENVIRONMENT') || 'sandbox',
        verbose: false,
        extended: true,
      };
      appleReceiptVerify.config(appleConfig);
    } catch (error) {
      this.logger.error(
        'Failed to initialize Apple receipt verification:',
        error,
      );
    }
  }

  private initializeAppleApiClient() {
    try {
      const appleApiKey = this.configService.get('APPLE_PRIVATE_KEY_PATH');
      const appleKeyId = this.configService.get('APPLE_KEY_ID');
      const appleIssuerId = this.configService.get('APPLE_ISSUER_ID');

      if (appleApiKey && appleKeyId && appleIssuerId) {
        this.appleApiClient = axios.create({
          baseURL: this.APPLE_API_BASE_URL,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        // Add request interceptor to include JWT token
        this.appleApiClient.interceptors.request.use((config: any) => {
          const token = this.generateAppleJWT();
          config.headers.Authorization = `Bearer ${token}`;
          return config;
        });

        this.logger.log('Apple App Store Connect API client initialized');
      } else {
        this.logger.warn('Apple API credentials not configured');
      }
    } catch (error) {
      this.logger.error('Failed to initialize Apple API client:', error);
    }
  }

  private generateAppleJWT(): string {
    const appleApiKey = this.configService.get('APPLE_PRIVATE_KEY_PATH');
    const appleKeyId = this.configService.get('APPLE_KEY_ID');
    const appleIssuerId = this.configService.get('APPLE_ISSUER_ID');

    if (!appleApiKey || !appleKeyId || !appleIssuerId) {
      throw new Error('Apple API credentials not configured');
    }

    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: appleIssuerId,
      iat: now,
      exp: now + 1200, // 20 minutes
      aud: 'appstoreconnect-v1',
    };

    const header = {
      alg: 'ES256',
      kid: appleKeyId,
      typ: 'JWT',
    };

    return jwt.sign(payload, appleApiKey, {
      algorithm: 'ES256',
      header,
    });
  }

  async convertRegionalPrices(
    packageName: string,
    basePrice: number,
    baseCurrency: string = 'AUD',
  ): Promise<any> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      const requestBody = {
        price: {
          currencyCode: baseCurrency,
          units: Math.floor(basePrice).toString(),
          nanos: Math.round((basePrice % 1) * 1e9),
        },
      };

      const response =
        await this.googlePlayPublisher.monetization.convertRegionPrices({
          packageName,
          requestBody,
        });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to convert regional prices:', error);
      throw error;
    }
  }

  async createGooglePlayProduct(
    packageName: string,
    productId: string,
    price: number,
    currency: string,
    title: string,
  ): Promise<any> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      // Step 1: Convert regional prices first
      const priceConversionResponse = await this.convertRegionalPrices(
        packageName,
        price,
        currency,
      );

      // Step 2: Extract converted prices and create regional configs
      const regionalConfigs: any = [];

      if (
        priceConversionResponse.convertedRegionPrices &&
        typeof priceConversionResponse.convertedRegionPrices === 'object'
      ) {
        for (const convertedPrice of Object.values(
          priceConversionResponse.convertedRegionPrices,
        ) as ConvertedPrice[]) {
          if (convertedPrice.regionCode) {
            regionalConfigs.push({
              regionCode: convertedPrice.regionCode,
              price: convertedPrice.price,
            });
          }
        }
      }

      // Fallback: If conversion failed or no converted prices, use base region only
      if (regionalConfigs.length === 0) {
        regionalConfigs.push({
          regionCode: 'AU',
          price: {
            currencyCode: currency,
            units: Math.floor(price).toString(),
            nanos: Math.round((price % 1) * 1e9),
          },
        });
      }

      const safeProductId = productId.replace(/\./g, '-');
      const basePlanId = `${safeProductId}-base-plan`;

      const requestBody = {
        basePlans: [
          {
            basePlanId,
            state: 'ACTIVE',
            regionalConfigs,
            autoRenewingBasePlanType: {
              billingPeriodDuration: 'P1M',
              gracePeriodDuration: 'P7D',
              accountHoldDuration: 'P30D',
              resubscribeState: ResubscribeState.RESUBSCRIBE_STATE_ACTIVE,
              prorationMode:
                SubscriptionProrationMode.SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE,
            },
          },
        ],
        listings: {
          languageCode: 'en-US',
          title: `${title} Plan`,
        },
      };

      // Step 3: Create the product with all regional pricing
      const response =
        await this.googlePlayPublisher.monetization.subscriptions.create({
          packageName,
          productId,
          'regionsVersion.version': '2025/01',
          requestBody,
        });

      // Step 4: Activate the base plan
      await this.googlePlayPublisher.monetization.subscriptions.basePlans.activate(
        {
          packageName,
          productId,
          basePlanId,
        },
      );

      return {
        subscription: response.data,
        regionalPricing: regionalConfigs,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create Google Play product ${productId}:`,
        error,
      );
      throw error;
    }
  }

  async updateGooglePlayProduct(
    packageName: string,
    productId: string,
    // price: number,
    // currency: string,
    title: string,
  ): Promise<any> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      // Step 1: Convert regional prices
      // const priceConversionResponse = await this.convertRegionalPrices(
      //   packageName,
      //   price,
      //   currency,
      // );

      // const regionalConfigs: any[] = [];

      // if (
      //   priceConversionResponse.convertedRegionPrices &&
      //   typeof priceConversionResponse.convertedRegionPrices === 'object'
      // ) {
      //   for (const convertedPrice of Object.values(
      //     priceConversionResponse.convertedRegionPrices,
      //   ) as ConvertedPrice[]) {
      //     if (convertedPrice.regionCode) {
      //       regionalConfigs.push({
      //         regionCode: convertedPrice.regionCode,
      //         price: convertedPrice.price,
      //       });
      //     }
      //   }
      // }

      // Fallback to base price if conversions fail
      // if (regionalConfigs.length === 0) {
      //   regionalConfigs.push({
      //     regionCode: 'AU',
      //     price: {
      //       currencyCode: currency,
      //       units: Math.floor(price).toString(),
      //       nanos: Math.round((price % 1) * 1e9),
      //     },
      //   });
      // }

      // const safeProductId = productId.replace(/\./g, '-');
      // const basePlanId = `${safeProductId}-base-plan`;

      const requestBody = {
        listings: {
          languageCode: 'en-US',
          title: `${title} Plan`,
        },
        // basePlans: [
        //   {
        //     basePlanId,
        //     state: 'ACTIVE',
        //     regionalConfigs,
        //     autoRenewingBasePlanType: {
        //       billingPeriodDuration: 'P1M',
        //       gracePeriodDuration: 'P7D',
        //       accountHoldDuration: 'P30D',
        //       resubscribeState: ResubscribeState.RESUBSCRIBE_STATE_ACTIVE,
        //       prorationMode:
        //         SubscriptionProrationMode.SUBSCRIPTION_PRORATION_MODE_CHARGE_ON_NEXT_BILLING_DATE,
        //     },
        //   },
        // ],
      };

      const response =
        await this.googlePlayPublisher.monetization.subscriptions.patch({
          packageName,
          productId,
          updateMask: 'listings', // Add here if base plan is updated
          'regionsVersion.version': '2025/01',
          requestBody,
        });

      return {
        subscription: response.data,
        // regionalPricing: regionalConfigs,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update Google Play product ${productId}:`,
        error,
      );
      throw error;
    }
  }

  async deleteGooglePlayProduct(
    packageName: string,
    productId: string,
  ): Promise<void> {
    if (!this.googlePlayPublisher) {
      throw new Error('Google Play API not initialized');
    }

    await this.googlePlayPublisher.monetization.subscriptions.delete({
      packageName,
      productId,
    });
  }

  // Apple Subscription Group Management
  async createAppleSubscriptionGroup(
    appId: string,
    referenceName: string,
  ): Promise<AppleSubscriptionGroup | null> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return null;
      }

      const requestBody = {
        data: {
          type: 'subscriptionGroups',
          attributes: {
            referenceName,
          },
          relationships: {
            app: {
              data: {
                type: 'apps',
                id: appId,
              },
            },
          },
        },
      };

      const response = await this.appleApiClient.post(
        '/subscriptionGroups',
        requestBody,
      );

      this.logger.log(
        `Apple subscription group created: ${response.data.data.id}`,
      );

      return {
        id: response.data.data.id,
        referenceName,
        appId,
        status: response.data.data.attributes.state,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create Apple subscription group: ${referenceName}`,
        error,
      );
      return null;
    }
  }

  async getAppleSubscriptionGroup(
    groupId: string,
  ): Promise<AppleSubscriptionGroup | null> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return null;
      }

      const response = await this.appleApiClient.get(
        `/subscriptionGroups/${groupId}`,
      );

      return {
        id: response.data.data.id,
        referenceName: response.data.data.attributes.referenceName,
        appId: response.data.data.relationships.app.data.id,
        status: response.data.data.attributes.state,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Apple subscription group: ${groupId}`,
        error,
      );
      return null;
    }
  }

  async updateAppleSubscriptionGroup(
    groupId: string,
    referenceName: string,
  ): Promise<AppleSubscriptionGroup | null> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return null;
      }

      const requestBody = {
        data: {
          type: 'subscriptionGroups',
          id: groupId,
          attributes: {
            referenceName,
          },
        },
      };

      const response = await this.appleApiClient.patch(
        `/subscriptionGroups/${groupId}`,
        requestBody,
      );

      this.logger.log(`Apple subscription group updated: ${groupId}`);

      return {
        id: response.data.data.id,
        referenceName: response.data.data.attributes.referenceName,
        appId: response.data.data.relationships.app.data.id,
        status: response.data.data.attributes.state,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update Apple subscription group: ${groupId}`,
        error,
      );
      return null;
    }
  }

  // Apple Subscription Product Management
  async createAppleSubscriptionProduct(
    subscriptionGroupId: string,
    productId: string,
    referenceName: string,
    subscriptionPeriod:
      | 'ONE_WEEK'
      | 'ONE_MONTH'
      | 'TWO_MONTHS'
      | 'THREE_MONTHS'
      | 'SIX_MONTHS'
      | 'ONE_YEAR' = 'ONE_MONTH',
    familyShareable: boolean = false,
    reviewNote?: string,
  ): Promise<AppleSubscriptionProduct | null> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return null;
      }

      const requestBody = {
        data: {
          type: 'subscriptions',
          attributes: {
            productId,
            referenceName,
            subscriptionPeriod,
            familyShareable,
            reviewNote: reviewNote || '',
          },
          relationships: {
            subscriptionGroup: {
              data: {
                type: 'subscriptionGroups',
                id: subscriptionGroupId,
              },
            },
          },
        },
      };

      const response = await this.appleApiClient.post(
        '/subscriptions',
        requestBody,
      );

      this.logger.log(
        `Apple subscription product created: ${response.data.data.id}`,
      );

      return {
        id: response.data.data.id,
        productId,
        referenceName,
        subscriptionGroupId,
        state: response.data.data.attributes.state,
        subscriptionPeriod,
        reviewNote,
        familyShareable,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create Apple subscription product: ${productId}`,
        error,
      );
      return null;
    }
  }

  async getAppleSubscriptionProduct(
    subscriptionId: string,
  ): Promise<AppleSubscriptionProduct | null> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return null;
      }

      const response = await this.appleApiClient.get(
        `/subscriptions/${subscriptionId}`,
      );

      return {
        id: response.data.data.id,
        productId: response.data.data.attributes.productId,
        referenceName: response.data.data.attributes.referenceName,
        subscriptionGroupId:
          response.data.data.relationships.subscriptionGroup.data.id,
        state: response.data.data.attributes.state,
        subscriptionPeriod: response.data.data.attributes.subscriptionPeriod,
        reviewNote: response.data.data.attributes.reviewNote,
        familyShareable: response.data.data.attributes.familyShareable,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Apple subscription product: ${subscriptionId}`,
        error,
      );
      return null;
    }
  }

  async updateAppleSubscriptionProduct(
    subscriptionId: string,
    updates: Partial<
      Pick<
        AppleSubscriptionProduct,
        'referenceName' | 'familyShareable' | 'reviewNote'
      >
    >,
  ): Promise<AppleSubscriptionProduct | null> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return null;
      }

      const requestBody = {
        data: {
          type: 'subscriptions',
          id: subscriptionId,
          attributes: updates,
        },
      };

      const response = await this.appleApiClient.patch(
        `/subscriptions/${subscriptionId}`,
        requestBody,
      );

      this.logger.log(`Apple subscription product updated: ${subscriptionId}`);

      return {
        id: response.data.data.id,
        productId: response.data.data.attributes.productId,
        referenceName: response.data.data.attributes.referenceName,
        subscriptionGroupId:
          response.data.data.relationships.subscriptionGroup.data.id,
        state: response.data.data.attributes.state,
        subscriptionPeriod: response.data.data.attributes.subscriptionPeriod,
        reviewNote: response.data.data.attributes.reviewNote,
        familyShareable: response.data.data.attributes.familyShareable,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update Apple subscription product: ${subscriptionId}`,
        error,
      );
      return null;
    }
  }

  // Apple Review Submission Management
  async submitAppleSubscriptionForReview(
    subscriptionIds: string[],
    reviewNote?: string,
  ): Promise<AppleReviewSubmission | null> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return null;
      }

      const items = subscriptionIds.map((id) => ({
        id,
        type: 'IN_APP_PURCHASE' as const,
      }));

      const requestBody = {
        data: {
          type: 'reviewSubmissions',
          attributes: {
            platform: 'IOS',
          },
          relationships: {
            items: {
              data: items.map((item) => ({
                type: 'reviewSubmissionItems',
                id: item.id,
              })),
            },
          },
        },
        included: items.map((item) => ({
          type: 'reviewSubmissionItems',
          id: item.id,
          relationships: {
            reviewSubmissionItem: {
              data: {
                type: 'inAppPurchases',
                id: item.id,
              },
            },
          },
        })),
      };

      const response = await this.appleApiClient.post(
        '/reviewSubmissions',
        requestBody,
      );

      this.logger.log(
        `Apple subscription review submission created: ${response.data.data.id}`,
      );

      return {
        id: response.data.data.id,
        platform: 'IOS',
        submittedDate: response.data.data.attributes.submittedDate,
        state: response.data.data.attributes.state,
        items,
      };
    } catch (error) {
      this.logger.error(
        'Failed to submit Apple subscription for review',
        error,
      );
      return null;
    }
  }

  async getAppleReviewSubmission(
    submissionId: string,
  ): Promise<AppleReviewSubmission | null> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return null;
      }

      const response = await this.appleApiClient.get(
        `/reviewSubmissions/${submissionId}`,
      );

      return {
        id: response.data.data.id,
        platform: response.data.data.attributes.platform,
        submittedDate: response.data.data.attributes.submittedDate,
        state: response.data.data.attributes.state,
        items: response.data.data.relationships.items.data.map((item: any) => ({
          id: item.id,
          type: item.type,
        })),
      };
    } catch (error) {
      this.logger.error(
        `Failed to get Apple review submission: ${submissionId}`,
        error,
      );
      return null;
    }
  }

  async cancelAppleReviewSubmission(submissionId: string): Promise<boolean> {
    try {
      if (!this.appleApiClient) {
        this.logger.warn('Apple API client not initialized');
        return false;
      }

      await this.appleApiClient.delete(`/reviewSubmissions/${submissionId}`);

      this.logger.log(`Apple review submission cancelled: ${submissionId}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to cancel Apple review submission: ${submissionId}`,
        error,
      );
      return false;
    }
  }

  // Legacy method for backward compatibility
  async createAppleProduct(
    bundleId: string,
    productId: string,
    displayName: string,
    description: string,
    price: string,
    currency: string,
  ): Promise<AppleProduct | null> {
    try {
      // For backward compatibility, create a mock product structure
      // In the new flow, this will be replaced by createAppleSubscriptionGroup and createAppleSubscriptionProduct
      const appleProduct: AppleProduct = {
        productId,
        bundleId,
        displayName,
        description,
        price,
        currency,
        subscriptionPeriod: 'P1M',
      };

      this.logger.log(`Apple product structure created (legacy): ${productId}`);
      return appleProduct;
    } catch (error) {
      this.logger.error(`Failed to create Apple product ${productId}:`, error);
      return null;
    }
  }

  async validateGooglePlayReceipt(
    packageName: string,
    productId: string,
    purchaseToken: string,
  ): Promise<ReceiptValidationResult> {
    try {
      if (!this.googlePlayPublisher) {
        throw new Error('Google Play API not initialized');
      }

      const response =
        await this.googlePlayPublisher.purchases.subscriptions.get({
          packageName: packageName,
          subscriptionId: productId,
          token: purchaseToken,
        });

      const purchase = response.data;
      const isValid = purchase.paymentState === 1; // 1 = Received
      const expirationDate = purchase.expiryTimeMillis
        ? new Date(parseInt(purchase.expiryTimeMillis))
        : undefined;

      return {
        isValid,
        productId,
        transactionId: purchase.orderId,
        purchaseDate: purchase.startTimeMillis
          ? new Date(parseInt(purchase.startTimeMillis))
          : undefined,
        expirationDate,
        platform: 'android',
        originalData: purchase,
      };
    } catch (error) {
      this.logger.error('Google Play receipt validation failed:', error);
      return {
        isValid: false,
        platform: 'android',
      };
    }
  }

  async validateAppleReceipt(
    receiptData: string,
  ): Promise<ReceiptValidationResult> {
    try {
      const result = await appleReceiptVerify.validate({
        receipt: receiptData,
        device: false,
      });

      if (result && result.receipt) {
        const latestReceiptInfo =
          result.latest_receipt_info?.[0] || result.receipt.in_app?.[0];

        return {
          isValid: true,
          productId: latestReceiptInfo?.product_id,
          transactionId: latestReceiptInfo?.transaction_id,
          purchaseDate: latestReceiptInfo?.purchase_date_ms
            ? new Date(parseInt(latestReceiptInfo.purchase_date_ms))
            : undefined,
          expirationDate: latestReceiptInfo?.expires_date_ms
            ? new Date(parseInt(latestReceiptInfo.expires_date_ms))
            : undefined,
          platform: 'ios',
          originalData: result,
        };
      }

      return {
        isValid: false,
        platform: 'ios',
      };
    } catch (error) {
      this.logger.error('Apple receipt validation failed:', error);
      return {
        isValid: false,
        platform: 'ios',
      };
    }
  }

  async checkSubscriptionStatus(
    platform: 'android' | 'ios',
    packageName: string,
    productId: string,
    token: string,
  ): Promise<{ isActive: boolean; expirationDate?: Date }> {
    try {
      if (platform === 'android') {
        const result = await this.validateGooglePlayReceipt(
          packageName,
          productId,
          token,
        );
        return {
          isActive:
            result.isValid && result.expirationDate
              ? result.expirationDate > new Date()
              : false,
          expirationDate: result.expirationDate,
        };
      } else {
        const result = await this.validateAppleReceipt(token);
        return {
          isActive:
            result.isValid && result.expirationDate
              ? result.expirationDate > new Date()
              : false,
          expirationDate: result.expirationDate,
        };
      }
    } catch (error) {
      this.logger.error('Subscription status check failed:', error);
      return { isActive: false };
    }
  }
}
