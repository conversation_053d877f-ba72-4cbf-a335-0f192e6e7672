import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
  <PERSON><PERSON>rray,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum AppleSubscriptionPeriod {
  ONE_WEEK = 'ONE_WEEK',
  ONE_MONTH = 'ONE_MONTH',
  TWO_MONTHS = 'TWO_MONTHS',
  THREE_MONTHS = 'THREE_MONTHS',
  SIX_MONTHS = 'SIX_MONTHS',
  ONE_YEAR = 'ONE_YEAR',
}

export enum AppleSubscriptionState {
  READY_FOR_SALE = 'READY_FOR_SALE',
  DEVELOPER_ACTION_NEEDED = 'DEVELOPER_ACTION_NEEDED',
  PENDING_DEVELOPER_RELEASE = 'PENDING_DEVELOPER_RELEASE',
  IN_REVIEW = 'IN_REVIEW',
  REJECTED = 'REJECTED',
}

export enum AppleReviewSubmissionState {
  READY_FOR_REVIEW = 'READY_FOR_REVIEW',
  IN_REVIEW = 'IN_REVIEW',
  PENDING_DEVELOPER_RELEASE = 'PENDING_DEVELOPER_RELEASE',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export class CreateAppleSubscriptionGroupDto {
  @ApiProperty({ description: 'Apple App ID from App Store Connect' })
  @IsString()
  @IsNotEmpty()
  appId: string;

  @ApiProperty({ description: 'Reference name for the subscription group' })
  @IsString()
  @IsNotEmpty()
  referenceName: string;
}

export class UpdateAppleSubscriptionGroupDto {
  @ApiProperty({ description: 'Reference name for the subscription group' })
  @IsString()
  @IsNotEmpty()
  referenceName: string;
}

export class CreateAppleSubscriptionProductDto {
  @ApiProperty({ description: 'Apple Subscription Group ID' })
  @IsString()
  @IsNotEmpty()
  subscriptionGroupId: string;

  @ApiProperty({ description: 'Product ID (must be unique in App Store Connect)' })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ description: 'Reference name for the subscription product' })
  @IsString()
  @IsNotEmpty()
  referenceName: string;

  @ApiProperty({ 
    description: 'Subscription period',
    enum: AppleSubscriptionPeriod,
    default: AppleSubscriptionPeriod.ONE_MONTH
  })
  @IsEnum(AppleSubscriptionPeriod)
  @IsOptional()
  subscriptionPeriod?: AppleSubscriptionPeriod = AppleSubscriptionPeriod.ONE_MONTH;

  @ApiProperty({ description: 'Whether the subscription is family shareable', default: false })
  @IsBoolean()
  @IsOptional()
  familyShareable?: boolean = false;

  @ApiProperty({ description: 'Review note for Apple submission', required: false })
  @IsString()
  @IsOptional()
  reviewNote?: string;
}

export class UpdateAppleSubscriptionProductDto {
  @ApiProperty({ description: 'Reference name for the subscription product', required: false })
  @IsString()
  @IsOptional()
  referenceName?: string;

  @ApiProperty({ description: 'Whether the subscription is family shareable', required: false })
  @IsBoolean()
  @IsOptional()
  familyShareable?: boolean;

  @ApiProperty({ description: 'Review note for Apple submission', required: false })
  @IsString()
  @IsOptional()
  reviewNote?: string;
}

export class AppleReviewSubmissionItemDto {
  @ApiProperty({ description: 'Apple Subscription Product ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: 'Item type', default: 'IN_APP_PURCHASE' })
  @IsString()
  @IsOptional()
  type?: string = 'IN_APP_PURCHASE';
}

export class CreateAppleReviewSubmissionDto {
  @ApiProperty({ 
    description: 'Array of subscription product IDs to submit for review',
    type: [AppleReviewSubmissionItemDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AppleReviewSubmissionItemDto)
  items: AppleReviewSubmissionItemDto[];

  @ApiProperty({ description: 'Review note for Apple submission', required: false })
  @IsString()
  @IsOptional()
  reviewNote?: string;
}

export class AppleSubscriptionGroupResponseDto {
  @ApiProperty({ description: 'Apple Subscription Group ID' })
  id?: string;

  @ApiProperty({ description: 'Reference name' })
  referenceName: string;

  @ApiProperty({ description: 'Apple App ID' })
  appId: string;

  @ApiProperty({ description: 'Current status', enum: AppleSubscriptionState })
  status?: AppleSubscriptionState;
}

export class AppleSubscriptionProductResponseDto {
  @ApiProperty({ description: 'Apple Subscription Product ID' })
  id?: string;

  @ApiProperty({ description: 'Product ID' })
  productId: string;

  @ApiProperty({ description: 'Reference name' })
  referenceName: string;

  @ApiProperty({ description: 'Subscription Group ID' })
  subscriptionGroupId: string;

  @ApiProperty({ description: 'Current state', enum: AppleSubscriptionState })
  state?: AppleSubscriptionState;

  @ApiProperty({ description: 'Subscription period', enum: AppleSubscriptionPeriod })
  subscriptionPeriod: AppleSubscriptionPeriod;

  @ApiProperty({ description: 'Review note' })
  reviewNote?: string;

  @ApiProperty({ description: 'Family shareable' })
  familyShareable?: boolean;
}

export class AppleReviewSubmissionResponseDto {
  @ApiProperty({ description: 'Review Submission ID' })
  id?: string;

  @ApiProperty({ description: 'Platform', default: 'IOS' })
  platform: 'IOS';

  @ApiProperty({ description: 'Submitted date' })
  submittedDate?: string;

  @ApiProperty({ description: 'Current state', enum: AppleReviewSubmissionState })
  state?: AppleReviewSubmissionState;

  @ApiProperty({ description: 'Submitted items', type: [AppleReviewSubmissionItemDto] })
  items: AppleReviewSubmissionItemDto[];
}

export class UpdateCommunityAppleStatusDto {
  @ApiProperty({ description: 'Apple Subscription Group ID', required: false })
  @IsString()
  @IsOptional()
  appleSubscriptionGroupId?: string;

  @ApiProperty({ description: 'Apple Subscription Product ID', required: false })
  @IsString()
  @IsOptional()
  appleSubscriptionId?: string;

  @ApiProperty({ description: 'Apple Review Submission ID', required: false })
  @IsString()
  @IsOptional()
  appleReviewSubmissionId?: string;

  @ApiProperty({ description: 'Review note for Apple submission', required: false })
  @IsString()
  @IsOptional()
  appleReviewNote?: string;

  @ApiProperty({ description: 'Rejection reason from Apple', required: false })
  @IsString()
  @IsOptional()
  appleReviewRejectionNote?: string;
}
