import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { UserCoreModule } from 'src/core/user-core/user-core.module';
import { UserSessionCoreModule } from 'src/core/user-session-core/user-session-core.module';
import { OTPVerificationCoreModule } from 'src/core/user-otp-verification-core/user-otp-verification-core.module';
import { AuthService } from './auth.service';
import { EmailService } from '../../shared/services/mailgun-email.service';

@Module({
  imports: [UserCoreModule, UserSessionCoreModule, OTPVerificationCoreModule],
  controllers: [AuthController],
  providers: [AuthService, EmailService],
  exports: [AuthService],
})
export class AuthModule {}
