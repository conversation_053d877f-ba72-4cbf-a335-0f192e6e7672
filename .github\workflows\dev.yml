on:
  push:
    branches:
      - dev
concurrency:
  group: DEV
  cancel-in-progress: true
name: 🚀 Deploy DEV to Server
jobs:
  deploy:
    # environment: Dev
    name: 🎉 Deploy
    runs-on: ubuntu-latest
    steps:
      - name: 🚚 SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DEV_SERVER_HOST }}
          username: ${{ secrets.DEV_SERVER_USER }}
          key: ${{ secrets.DEV_SERVER_KEY }}
          port: ${{ secrets.DEV_SERVER_PORT }}
          script: |
            cd /var/www/jymfitnessx-node/
            git reset --hard
            git checkout dev
            git pull origin dev
            yarn
            export NODE_ENV=production
            yarn prisma-all
            pm2 delete "dev-jymfitnessx-node"
            yarn build
            pm2 start "/var/www/jymfitnessx-node/dist/src/main.js" --name="dev-jymfitnessx-node" -i 1
            pm2 save