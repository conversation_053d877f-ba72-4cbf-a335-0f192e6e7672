import { Global, Module } from '@nestjs/common';
import { PrismaService } from 'src/shared/module/prisma/prisma.service';
import { UserSessionCoreService } from './user-session-core.service';
import { PrismaModule } from 'src/shared/module/prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [UserSessionCoreService, PrismaService],
  exports: [UserSessionCoreService],
})
export class UserSessionCoreModule {}
