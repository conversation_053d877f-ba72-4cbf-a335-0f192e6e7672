import { Modu<PERSON> } from '@nestjs/common';
import { CommunityController } from './community.controller';
import { CommunityService } from './community.service';
import { CommunityCoreModule } from 'src/core/community-core/community-core.module';
import { PriceModelCoreModule } from 'src/core/price-model-core/price-model-core.module';
import { MemberPurchaseCoreModule } from 'src/core/member-purchase-core/member-purchase-core.module';
import { MemberDetailsCoreModule } from 'src/core/member-details-core/member-details-core.module';
import { AuthModule } from '../auth/auth.module';
import { IAPModule } from '../iap/iap.module';

@Module({
  imports: [
    CommunityCoreModule,
    PriceModelCoreModule,
    MemberPurchaseCoreModule,
    MemberDetailsCoreModule,
    AuthModule,
    IAPModule,
  ],
  controllers: [CommunityController],
  providers: [CommunityService],
  exports: [CommunityService],
})
export class CommunityModule {}