import {
  Injectable,
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { USER_TYPE, COMMUNITY_STATUS, Community } from '@prisma/client';
import { CommunityCoreService } from 'src/core/community-core/community-core.service';
import { PriceModelCoreService } from 'src/core/price-model-core/price-model-core.service';
import { MemberPurchaseCoreService } from 'src/core/member-purchase-core/member-purchase-core.service';
import { MemberDetailsCoreService } from 'src/core/member-details-core/member-details-core.service';
import { IAPService } from 'src/module/iap/iap.service';
import { UserSessionType } from 'src/shared/types/user-session.type';
import { BaseQueryCoreDto } from 'src/core/base-query-core/dto/base-query-core.dto';
import {
  CreateCommunityDto,
  UpdateCommunityDto,
  UpdateCommunityStatusDto,
  Join<PERSON>ommunityDto,
  CreateAppleCommunitySubscriptionDto,
  SubmitAppleCommunityForReviewDto,
} from './dto/community.dto';
import {
  communityMessage,
  memberPurchaseMessage,
  iapMessage,
  userMessage,
} from 'src/shared/keys/helper.key';
import { PLATFORM, MEMBERSHIP_STATUS, STATUS } from '@prisma/client';

@Injectable()
export class CommunityService {
  private readonly logger = new Logger(CommunityService.name);

  constructor(
    private readonly communityCoreService: CommunityCoreService,
    private readonly priceModelCoreService: PriceModelCoreService,
    private readonly memberPurchaseCoreService: MemberPurchaseCoreService,
    private readonly memberDetailsCoreService: MemberDetailsCoreService,
    private readonly iapService: IAPService,
  ) {}

  async createCommunity(dto: CreateCommunityDto, sessionData: UserSessionType) {
    // Only owners can create communities
    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(
        communityMessage.COMMUNITY_CREATE_FORBIDDEN,
      );
    }

    let priceId: string | undefined;

    // Handle pricing if not free
    if (!dto.isFree && dto.priceAmount) {
      const priceModel = await this.priceModelCoreService.createPriceModel(
        dto.priceAmount,
        dto.priceCurrency || 'AUD',
      );
      priceId = priceModel.id;
    }

    // Create community with PENDING status
    const community = await this.communityCoreService.create({
      data: {
        userId: sessionData.user.id,
        name: dto.name,
        description: dto.description,
        image: dto.image,
        banner: dto.banner,
        videoUrl: dto.videoUrl,
        isFree: dto.isFree,
        priceId: priceId,
        communityStatus: COMMUNITY_STATUS.PENDING,
      },
    });

    // If not free, create products in app stores
    if (!dto.isFree && dto.priceAmount) {
      await this.createAppStoreProducts(community.id, dto, sessionData);
    } else {
      // For free communities, auto-approve
      await this.autoApproveCommunity(community.id);
    }

    // Fetch updated community with product IDs
    const updatedCommunity = await this.communityCoreService.findUnique({
      where: { id: community.id },
      include: {
        price: true,
      },
    });

    return {
      status: true,
      message: communityMessage.COMMUNITY_CREATED,
      community: updatedCommunity,
    };
  }

  private async createAppStoreProducts(
    communityId: string,
    dto: CreateCommunityDto,
    sessionData: UserSessionType,
  ) {
    try {
      const formatIdSuffix = (id: string): string => id.slice(-12);
      const productId = `community.${formatIdSuffix(communityId)}.${formatIdSuffix(sessionData.user.id)}`;

      // Create Google Play product (auto-approved)
      const googleProduct = await this.iapService.createGooglePlayProduct(
        process.env.GOOGLE_PLAY_PACKAGE_NAME!,
        productId,
        dto.priceAmount!,
        dto.priceCurrency || 'AUD',
        dto.name,
      );

      // For Apple, create subscription group and product but don't auto-approve
      await this.createAppleSubscriptionFlow(communityId, productId, dto, sessionData);

      // Update community with Google product ID only (Apple will be updated after approval)
      await this.communityCoreService.update({
        where: { id: communityId },
        data: {
          googleProductId: googleProduct?.subscription?.productId,
          communityStatus: COMMUNITY_STATUS.PENDING, // Keep pending until Apple approval
        },
      });
    } catch (error) {
      console.error('Failed to create app store products:', error);
    }
  }

  private async createAppleSubscriptionFlow(
    communityId: string,
    productId: string,
    dto: CreateCommunityDto,
    sessionData: UserSessionType,
  ) {
    try {
      const appId = process.env.APPLE_APP_ID;
      if (!appId) {
        this.logger.warn('Apple App ID not configured, skipping Apple subscription creation');
        return;
      }

      // Step 1: Create Apple Subscription Group
      const subscriptionGroup = await this.iapService.createAppleSubscriptionGroup(
        appId,
        `${dto.name} Subscription Group`,
      );

      if (!subscriptionGroup?.id) {
        this.logger.error('Failed to create Apple subscription group');
        return;
      }

      // Step 2: Create Apple Subscription Product
      const subscriptionProduct = await this.iapService.createAppleSubscriptionProduct(
        subscriptionGroup.id,
        productId,
        `${dto.name} Monthly Subscription`,
        'ONE_MONTH',
        false,
        dto.description || `Monthly subscription for ${dto.name} community`,
      );

      if (!subscriptionProduct?.id) {
        this.logger.error('Failed to create Apple subscription product');
        return;
      }

      // Step 3: Update community with Apple subscription details
      await this.communityCoreService.update({
        where: { id: communityId },
        data: {
          appleSubscriptionGroupId: subscriptionGroup.id,
          appleSubscriptionId: subscriptionProduct.id,
          appleProductId: subscriptionProduct.productId,
          appleReviewNote: dto.description || `Monthly subscription for ${dto.name} community`,
        },
      });

      this.logger.log(`Apple subscription flow created for community: ${communityId}`);
    } catch (error) {
      this.logger.error('Failed to create Apple subscription flow:', error);
    }
  }

  // Apple Subscription Management Methods
  async createAppleCommunitySubscription(
    dto: CreateAppleCommunitySubscriptionDto,
    sessionData: UserSessionType,
  ) {
    const community = await this.communityCoreService.findUnique({
      where: { id: dto.communityId },
    });

    if (!community) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_FOUND);
    }

    // Check if user owns the community
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    try {
      const formatIdSuffix = (id: string): string => id.slice(-12);
      const productId = `community.${formatIdSuffix(dto.communityId)}.${formatIdSuffix(sessionData.user.id)}`;

      // Step 1: Create Apple Subscription Group
      const subscriptionGroup = await this.iapService.createAppleSubscriptionGroup(
        dto.appId,
        `${community.name} Subscription Group`,
      );

      if (!subscriptionGroup?.id) {
        throw new BadRequestException('Failed to create Apple subscription group');
      }

      // Step 2: Create Apple Subscription Product
      const subscriptionProduct = await this.iapService.createAppleSubscriptionProduct(
        subscriptionGroup.id,
        productId,
        `${community.name} Subscription`,
        dto.subscriptionPeriod || 'ONE_MONTH',
        dto.familyShareable || false,
        dto.reviewNote || `Subscription for ${community.name} community`,
      );

      if (!subscriptionProduct?.id) {
        throw new BadRequestException('Failed to create Apple subscription product');
      }

      // Step 3: Update community with Apple subscription details
      const updatedCommunity = await this.communityCoreService.update({
        where: { id: dto.communityId },
        data: {
          // appleSubscriptionGroupId: subscriptionGroup.id,
          // appleSubscriptionId: subscriptionProduct.id,
          appleProductId: subscriptionProduct.productId,
          // appleReviewNote: dto.reviewNote || `Subscription for ${community.name} community`,
          communityStatus: COMMUNITY_STATUS.PENDING,
        },
      });

      return {
        status: true,
        message: 'Apple subscription created successfully. Submit for review to activate.',
        community: updatedCommunity,
        subscriptionGroup,
        subscriptionProduct,
      };
    } catch (error) {
      this.logger.error('Failed to create Apple community subscription:', error);
      throw new BadRequestException('Failed to create Apple subscription');
    }
  }

  async submitAppleCommunityForReview(
    dto: SubmitAppleCommunityForReviewDto,
    sessionData: UserSessionType,
  ) {
    const community = await this.communityCoreService.findUnique({
      where: { id: dto.communityId },
    });

    if (!community) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_FOUND);
    }

    // Check if user owns the community
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    if (!community.appleProductId) {
      throw new BadRequestException('Apple subscription not created for this community');
    }

    try {
      // Submit Apple subscription for review
      const reviewSubmission = await this.iapService.submitAppleSubscriptionForReview(
        [community.appleProductId],
        dto.reviewNote,
      );

      if (!reviewSubmission?.id) {
        throw new BadRequestException('Failed to submit Apple subscription for review');
      }

      // Update community status
      const updatedCommunity = await this.communityCoreService.update({
        where: { id: dto.communityId },
        data: {
          // appleReviewSubmissionId: reviewSubmission.id,
          // appleReviewNote: dto.reviewNote,
          // appleSubmittedAt: new Date(),
          communityStatus: COMMUNITY_STATUS.APPLE_REVIEW_PENDING,
        },
      });

      return {
        status: true,
        message: 'Community submitted for Apple review successfully',
        community: updatedCommunity,
        reviewSubmission,
      };
    } catch (error) {
      this.logger.error('Failed to submit Apple community for review:', error);
      throw new BadRequestException('Failed to submit for Apple review');
    }
  }

  async handleAppleReviewApproval(communityId: string) {
    try {
      const updatedCommunity = await this.communityCoreService.update({
        where: { id: communityId },
        data: {
          communityStatus: COMMUNITY_STATUS.ACTIVE,
          // appleApprovedAt: new Date(),
        },
      });

      this.logger.log(`Community approved by Apple: ${communityId}`);
      return updatedCommunity;
    } catch (error) {
      this.logger.error('Failed to handle Apple review approval:', error);
      throw error;
    }
  }

  async handleAppleReviewRejection(communityId: string, rejectionNote: string) {
    try {
      const updatedCommunity = await this.communityCoreService.update({
        where: { id: communityId },
        data: {
          communityStatus: COMMUNITY_STATUS.APPLE_REVIEW_REJECTED,
          // appleReviewRejectionNote: rejectionNote,
          // appleRejectedAt: new Date(),
        },
      });

      this.logger.log(`Community rejected by Apple: ${communityId}`);
      return updatedCommunity;
    } catch (error) {
      this.logger.error('Failed to handle Apple review rejection:', error);
      throw error;
    }
  }

  async updateCommunity(
    communityId: string,
    dto: UpdateCommunityDto,
    sessionData: UserSessionType,
  ) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId, isDeleted: false },
    });

    if (!community) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_FOUND);
    }

    // Check if user owns the community and community is active
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(
        communityMessage.COMMUNITY_UPDATE_FORBIDDEN,
      );
    }

    if (community.status !== STATUS.ENABLED || community.isDeleted) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_DELETED);
    }

    // let priceId = community.priceId;

    // Handle pricing changes
    // if (dto.isFree !== undefined || dto.priceAmount) {
    //   if (!dto.isFree && dto.priceAmount) {
    //     const existingPrice =
    //       await this.priceModelCoreService.findByAmountAndInterval(
    //         dto.priceAmount,
    //         dto.priceCurrency || 'AUD',
    //       );

    //     if (existingPrice) {
    //       priceId = existingPrice.id;
    //     } else {
    //       const priceModel = await this.priceModelCoreService.createPriceModel(
    //         dto.priceAmount,
    //         dto.priceCurrency || 'AUD',
    //       );
    //       priceId = priceModel.id;
    //     }
    //   } else if (dto.isFree) {
    //     priceId = null;
    //   }
    // }

    // If not free, update products in app stores
    // if (!dto.isFree && dto.priceAmount) {
    //   await this.updateAppStoreProducts(communityId, dto, sessionData);
    // }

    if (!community.isFree && community.priceId) {
      await this.updateAppStoreProducts(communityId, dto, sessionData);
    }

    const updatedCommunity = await this.communityCoreService.update({
      where: { id: communityId },
      data: {
        name: dto.name,
        description: dto.description,
        image: dto.image,
        banner: dto.banner,
        videoUrl: dto.videoUrl,
        // isFree: dto.isFree,
        // priceId: priceId,
      },
      include: {
        price: true,
      },
    });

    return {
      status: true,
      message: communityMessage.COMMUNITY_UPDATED,
      community: updatedCommunity,
    };
  }

  private async updateAppStoreProducts(
    communityId: string,
    dto: UpdateCommunityDto,
    sessionData: UserSessionType,
  ) {
    try {
      const formatIdSuffix = (id: string): string => id.slice(-12);
      const productId = `community.${formatIdSuffix(communityId)}.${formatIdSuffix(sessionData.user.id)}`;

      // Update Google Play product
      const googleProduct = await this.iapService.updateGooglePlayProduct(
        process.env.GOOGLE_PLAY_PACKAGE_NAME!,
        productId,
        // dto.priceAmount!,
        // dto.priceCurrency || 'AUD',
        dto.name,
      );

      // Update Apple product
      // const appleProduct = await this.iapService.updateAppleProduct(
      //   process.env.APPLE_BUNDLE_ID!,
      //   productId,
      //   dto.name,
      //   dto.description || '',
      //   dto.priceAmount!.toString(),
      //   dto.priceCurrency || 'AUD',
      // );

      // Optionally log or update metadata in DB if needed
      await this.communityCoreService.update({
        where: { id: communityId },
        data: {
          updatedAt: new Date(),
          googleProductId: googleProduct?.subscription?.productId,
          // appleProductId: appleProduct?.productId,
        },
      });
    } catch (error) {
      console.error('Failed to update app store products:', error);
      throw error;
    }
  }

  async deleteCommunity(communityId: string, sessionData: UserSessionType) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId, isDeleted: false },
    });

    if (!community) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_FOUND);
    }

    // Check if user owns the community
    if (community && community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(
        communityMessage.COMMUNITY_UPDATE_FORBIDDEN,
      );
    }

    if (community.status !== STATUS.ENABLED || community.isDeleted) {
      throw new UnauthorizedException(
        communityMessage.COMMUNITY_IS_ALREDY_DELETED,
      );
    }

    // Attempt to delete Google Play subscription product if it exists
    if (
      community.googleProductId &&
      community.communityStatus === COMMUNITY_STATUS.PENDING
    ) {
      try {
        await this.iapService.deleteGooglePlayProduct(
          process.env.GOOGLE_PLAY_PACKAGE_NAME!,
          community.googleProductId,
        );
      } catch (error) {
        console.error(error);
      }
    }

    // Soft delete the community
    await this.communityCoreService.update({
      where: { id: communityId },
      data: { isDeleted: true, status: STATUS.DISABLED },
    });

    return {
      status: true,
      message: communityMessage.COMMUNITY_DELETED,
    };
  }

  async updateCommunityStatus(
    communityId: string,
    dto: UpdateCommunityStatusDto,
    sessionData: UserSessionType,
  ) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    if (!community) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_FOUND);
    }

    // Check if user owns the community
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    const updatedCommunity = await this.communityCoreService.update({
      where: { id: communityId },
      data: { communityStatus: dto.communityStatus },
    });

    return {
      status: true,
      message: communityMessage.COMMUNITY_STATUS_UPDATED,
      community: updatedCommunity,
    };
  }

  async getOwnerCommunities(
    baseQueryCoreDto: BaseQueryCoreDto,
    sessionData: UserSessionType,
  ) {
    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    const where: any = {
      userId: sessionData.user.id,
      isDeleted: false,
    };

    const communities = await this.communityCoreService.findPaginate(
      baseQueryCoreDto,
      where,
    );

    return {
      status: true,
      communities,
    };
  }

  async joinCommunity(dto: JoinCommunityDto, sessionData: UserSessionType) {
    const community = await this.communityCoreService.findUnique({
      where: { id: dto.communityId },
      include: { price: true },
    });

    // Check if community is active
    if (community.communityStatus !== COMMUNITY_STATUS.ACTIVE) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_ACTIVE);
    }

    // Check if user already joined
    const existingPurchase = await this.memberPurchaseCoreService.findFirst({
      where: {
        memberId: sessionData.user.id,
        communityId: dto.communityId,
        membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
      },
    });

    if (existingPurchase) {
      throw new BadRequestException(communityMessage.COMMUNITY_ALREADY_JOINED);
    }

    // Handle paid community
    if (!community.isFree) {
      if (!dto.receiptData || !dto.platform) {
        throw new BadRequestException(
          communityMessage.COMMUNITY_PURCHASE_REQUIRED,
        );
      }

      // Validate receipt
      const validationResult = await this.validatePurchaseReceipt(
        dto.platform,
        community.googleProductId || community.appleProductId || '',
        dto.receiptData,
        dto.packageName,
      );

      if (!validationResult.isValid) {
        throw new BadRequestException(memberPurchaseMessage.PURCHASE_INVALID);
      }

      // Create purchase record
      await this.memberPurchaseCoreService.create({
        data: {
          memberId: sessionData.user.id,
          communityId: dto.communityId,
          productId: validationResult.productId || '',
          platform: dto.platform,
          expiresAt: validationResult.expirationDate,
        },
      });
    }

    // Check if member already exists
    const existingMember = await this.memberDetailsCoreService.findFirst({
      where: {
        userId: sessionData.user.id,
        communityId: dto.communityId,
      },
    });

    // Create only if not exists
    if (!existingMember) {
      await this.memberDetailsCoreService.create({
        data: {
          userId: sessionData.user.id,
          communityId: dto.communityId,
        },
      });
    }

    return {
      status: true,
      message: 'Successfully joined community',
    };
  }

  private async validatePurchaseReceipt(
    platform: PLATFORM,
    productId: string,
    receiptData: string,
    packageName?: string,
  ) {
    if (platform === PLATFORM.ANDROID) {
      return this.iapService.validateGooglePlayReceipt(
        packageName || '',
        productId,
        receiptData,
      );
    } else {
      return this.iapService.validateAppleReceipt(receiptData);
    }
  }

  async getMemberPurchases(
    baseQueryCoreDto: BaseQueryCoreDto,
    sessionData: UserSessionType,
  ) {
    if (
      sessionData.user.isDeleted &&
      sessionData.user.status !== STATUS.ENABLED
    ) {
      throw new UnauthorizedException(userMessage.USER_IS_DELETED);
    }

    const where: any = {
      memberId: sessionData.user.id,
    };

    const purchases = await this.memberPurchaseCoreService.findPaginate(
      baseQueryCoreDto,
      where,
    );

    return {
      status: true,
      purchases,
    };
  }

  async getCommunityMembers(
    communityId: string,
    baseQueryCoreDto: BaseQueryCoreDto,
    sessionData: UserSessionType,
  ) {
    const community = await this.communityCoreService.findUnique({
      where: { id: communityId },
    });

    if (!community) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_FOUND);
    }

    // Check if user owns the community
    if (community.userId !== sessionData.user.id) {
      throw new UnauthorizedException(communityMessage.COMMUNITY_NOT_OWNED);
    }

    const where: any = {
      communityId,
    };

    const purchases = await this.memberPurchaseCoreService.findPaginate(
      baseQueryCoreDto,
      where,
    );

    return {
      status: true,
      members: purchases,
    };
  }

  async getAvailableCommunities(
    baseQueryCoreDto: BaseQueryCoreDto,
    sessionData: UserSessionType,
  ) {
    if (
      sessionData.user.isDeleted &&
      sessionData.user.status !== STATUS.ENABLED
    ) {
      throw new UnauthorizedException(userMessage.USER_IS_DELETED);
    }

    // Step 1: Get all community IDs user already joined
    const userPurchases = await this.memberPurchaseCoreService.findMany({
      where: { memberId: sessionData.user.id },
      select: { communityId: true },
    });

    const joinedCommunityIds = userPurchases.map((p) => p.communityId);

    // Step 2: Add exclusion in query
    const where: any = {
      communityStatus: COMMUNITY_STATUS.ACTIVE,
      isDeleted: false,
      id: {
        notIn: joinedCommunityIds.length ? joinedCommunityIds : ['dummy'],
      },
    };

    // Step 3: Fetch only unjoined communities
    const communities = await this.communityCoreService.findPaginate(
      baseQueryCoreDto,
      where,
    );

    return {
      status: true,
      communities,
    };
  }

  async getCommunityDetails(communityId: string, sessionData: UserSessionType) {
    const community = await this.communityCoreService.findFirst({
      where: { id: communityId, isDeleted: false },
      include: {
        price: true,
        user: true,
      },
    });

    if (!community) {
      throw new BadRequestException(communityMessage.COMMUNITY_NOT_FOUND);
    }

    return {
      status: true,
      community,
    };
  }

  async checkSubscriptionStatus(
    communityId: string,
    sessionData: UserSessionType,
  ) {
    const purchase = await this.memberPurchaseCoreService.findFirst({
      where: {
        memberId: sessionData.user.id,
        communityId,
        membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
      },
    });

    if (!purchase) {
      return {
        status: true,
        isSubscribed: false,
        message: 'No active subscription found',
      };
    }

    // Check if subscription is still valid
    const isActive =
      purchase.membershipStatus === MEMBERSHIP_STATUS.ACTIVE &&
      (!purchase.expiresAt || purchase.expiresAt > new Date());

    return {
      status: true,
      isSubscribed: isActive,
      purchase,
      expiresAt: purchase.expiresAt,
    };
  }

  private async autoApproveCommunity(communityId: string) {
    try {
      await this.communityCoreService.update({
        where: { id: communityId },
        data: { communityStatus: COMMUNITY_STATUS.ACTIVE },
      });
    } catch (error) {
      this.logger.error(
        `Failed to auto-approve community ${communityId}:`,
        error,
      );
    }
  }
}
